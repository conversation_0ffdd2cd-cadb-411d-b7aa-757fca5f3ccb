import React from 'react';
import styled from 'styled-components';
import { usePermissions } from '../hooks/usePermissions';
import { FaLock, FaExclamationTriangle } from 'react-icons/fa';

const AccessDeniedContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const LockIcon = styled.div`
  font-size: 64px;
  color: #dc3545;
  margin-bottom: 20px;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
`;

const Title = styled.h2`
  color: #2c3e50;
  margin-bottom: 16px;
  font-size: 24px;
`;

const Message = styled.p`
  color: #6c757d;
  font-size: 16px;
  line-height: 1.6;
  max-width: 500px;
  margin-bottom: 20px;
`;

const ContactInfo = styled.div`
  background: white;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  margin-top: 20px;
`;

const ContactTitle = styled.h4`
  color: #007bff;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ContactText = styled.p`
  color: #495057;
  margin: 0;
  font-size: 14px;
`;

/**
 * Componente para proteger conteúdo baseado em permissões
 * @param {string|string[]} permission - Permissão(ões) necessária(s)
 * @param {boolean} requireAll - Se true, requer todas as permissões. Se false, requer pelo menos uma
 * @param {React.ReactNode} children - Conteúdo a ser renderizado se tiver permissão
 * @param {React.ReactNode} fallback - Conteúdo alternativo se não tiver permissão
 * @param {string} customMessage - Mensagem personalizada de acesso negado
 */
const PermissionGuard = ({ 
  permission, 
  requireAll = false, 
  children, 
  fallback = null,
  customMessage = null 
}) => {
  const { hasPermission, hasAllPermissions, hasAnyPermission, user } = usePermissions();

  // Verificar se tem permissão
  let hasAccess = false;

  if (typeof permission === 'string') {
    hasAccess = hasPermission(permission);
  } else if (Array.isArray(permission)) {
    hasAccess = requireAll 
      ? hasAllPermissions(permission)
      : hasAnyPermission(permission);
  } else {
    // Se não especificou permissão, permitir acesso
    hasAccess = true;
  }

  // Se tem acesso, renderizar o conteúdo
  if (hasAccess) {
    return children;
  }

  // Se tem fallback personalizado, usar ele
  if (fallback) {
    return fallback;
  }

  // Renderizar tela de acesso negado padrão
  const getPermissionName = (perm) => {
    const permissionNames = {
      gerarSenha: 'Gerar Senhas',
      chamarSenha: 'Chamar Senhas',
      finalizarSenha: 'Finalizar Senhas',
      acessarPainel: 'Acessar Painel',
      acessarEstatisticas: 'Acessar Estatísticas',
      acessarConfiguracao: 'Acessar Configurações',
      acessarDesign: 'Acessar Design',
      gerenciarPedidos: 'Gerenciar Pedidos',
      criarQRCodePedidos: 'Criar QR Code de Pedidos',
      limparDados: 'Limpar Dados',
      exportarDados: 'Exportar Dados',
      configurarTiposSenha: 'Configurar Tipos de Senha',
      configurarSom: 'Configurar Som',
      configurarLayout: 'Configurar Layout',
      permitirAcessoPublico: 'Permitir Acesso Público',
      editarDadosEmpresa: 'Editar Dados da Empresa',
      alterarSenha: 'Alterar Senha'
    };
    return permissionNames[perm] || perm;
  };

  const getRequiredPermissions = () => {
    if (typeof permission === 'string') {
      return getPermissionName(permission);
    } else if (Array.isArray(permission)) {
      const permNames = permission.map(getPermissionName);
      if (requireAll) {
        return permNames.join(' e ');
      } else {
        return permNames.join(' ou ');
      }
    }
    return 'permissões específicas';
  };

  return (
    <AccessDeniedContainer>
      <LockIcon>
        <FaLock />
      </LockIcon>
      
      <Title>Acesso Restrito</Title>
      
      <Message>
        {customMessage || (
          <>
            Você não tem permissão para acessar esta funcionalidade. 
            É necessário ter a permissão: <strong>{getRequiredPermissions()}</strong>
          </>
        )}
      </Message>

      <ContactInfo>
        <ContactTitle>
          <FaExclamationTriangle />
          Precisa de Acesso?
        </ContactTitle>
        <ContactText>
          Entre em contato com o administrador do sistema para solicitar as permissões necessárias.
        </ContactText>
        <ContactText style={{ marginTop: '8px' }}>
          <strong>Empresa:</strong> {user?.companyName || 'Não informado'}
        </ContactText>
        <ContactText>
          <strong>Email:</strong> {user?.email || 'Não informado'}
        </ContactText>
      </ContactInfo>
    </AccessDeniedContainer>
  );
};

/**
 * Componente para ocultar elementos baseado em permissões
 * Mais leve que o PermissionGuard, apenas oculta sem mostrar mensagem
 */
export const PermissionHidden = ({ permission, requireAll = false, children }) => {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermissions();

  let hasAccess = false;

  if (typeof permission === 'string') {
    hasAccess = hasPermission(permission);
  } else if (Array.isArray(permission)) {
    hasAccess = requireAll 
      ? hasAllPermissions(permission)
      : hasAnyPermission(permission);
  } else {
    hasAccess = true;
  }

  return hasAccess ? children : null;
};

/**
 * Componente para desabilitar elementos baseado em permissões
 */
export const PermissionDisabled = ({ 
  permission, 
  requireAll = false, 
  children, 
  disabledProps = { disabled: true, style: { opacity: 0.5, cursor: 'not-allowed' } }
}) => {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermissions();

  let hasAccess = false;

  if (typeof permission === 'string') {
    hasAccess = hasPermission(permission);
  } else if (Array.isArray(permission)) {
    hasAccess = requireAll 
      ? hasAllPermissions(permission)
      : hasAnyPermission(permission);
  } else {
    hasAccess = true;
  }

  if (hasAccess) {
    return children;
  }

  // Clonar o elemento filho e adicionar props de desabilitado
  return React.cloneElement(children, disabledProps);
};

export default PermissionGuard;
