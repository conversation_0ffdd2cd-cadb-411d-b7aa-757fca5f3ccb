import axios from './api';

// Função para obter o token JWT
const getToken = () => {
  return localStorage.getItem('token');
};

// Função para verificar se o usuário está autenticado
export const isAuthenticated = () => {
  return !!getToken();
};

// Função para obter o usuário atual
export const getCurrentUser = async () => {
  try {
    const token = getToken();
    if (!token) return null;

    const response = await axios.get(`/api/me`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao obter usuário atual:', error);
    return null;
  }
};

// Função para atualizar as configurações do usuário
export const updateUserConfig = async (config) => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    const response = await axios.put(
      `/api/me/config`,
      { config },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    return response.data;
  } catch (error) {
    console.error('Erro ao atualizar configurações:', error);
    throw error;
  }
};

// Função para verificar conectividade com o servidor
export const verificarConectividade = async () => {
  try {
    await axios.get(`/api/check-connection`);
    return true;
  } catch (error) {
    console.warn('Erro ao verificar conectividade:', error);
    return false;
  }
};

// Função para fazer login
export const login = async (email, password) => {
  try {
    if (!email || !email.trim()) {
      throw new Error('Email é obrigatório');
    }

    if (!password || !password.trim()) {
      throw new Error('Senha é obrigatória');
    }

    const trimmedEmail = email.trim();
    const trimmedPassword = password.trim();

    console.log('Enviando requisição de login:', {
      email: trimmedEmail,
      passwordLength: trimmedPassword.length
    });

    try {
      // Verificar conectividade com o servidor antes de tentar login
      await verificarConectividade();

      const response = await axios.post(`/api/login`, {
        email: trimmedEmail,
        password: trimmedPassword
      });

      console.log('Resposta do servidor recebida:', {
        status: response.status,
        statusText: response.statusText,
        hasToken: !!response.data?.token,
        hasUser: !!response.data?.user
      });

      const { token, user } = response.data;

      if (token) {
        localStorage.setItem('token', token);

        // Salvar dados básicos do usuário para recuperação em caso de problemas de rede
        if (user) {
          localStorage.setItem('userEmail', user.email || '');
          localStorage.setItem('companyName', user.companyName || '');
          localStorage.setItem('userId', user._id || user.id || '');
        }

        return { token, user };
      }

      throw new Error('Token não recebido do servidor');
    } catch (axiosError) {
      console.error('Erro detalhado na requisição:', {
        message: axiosError.message,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data
      });
      throw axiosError;
    }
  } catch (error) {
    console.error('Erro no login:', error.response?.data || error);
    throw error;
  }
};

// Função para registrar um novo usuário
export const register = async (email, password, companyName) => {
  try {
    if (!email || !email.trim()) {
      throw new Error('Email é obrigatório');
    }

    if (!password || !password.trim()) {
      throw new Error('Senha é obrigatória');
    }

    if (!companyName || !companyName.trim()) {
      throw new Error('Nome da empresa é obrigatório');
    }

    const trimmedEmail = email.trim();
    const trimmedPassword = password.trim();
    const trimmedCompanyName = companyName.trim();

    console.log('Enviando requisição de registro:', {
      email: trimmedEmail,
      companyName: trimmedCompanyName,
      passwordLength: trimmedPassword.length
    });

    const response = await axios.post(`/api/register`, {
      email: trimmedEmail,
      password: trimmedPassword,
      companyName: trimmedCompanyName
    });

    const { token, user } = response.data;

    if (token) {
      localStorage.setItem('token', token);

      // Salvar dados básicos do usuário para recuperação em caso de problemas de rede
      if (user) {
        localStorage.setItem('userEmail', user.email || '');
        localStorage.setItem('companyName', user.companyName || '');
        localStorage.setItem('userId', user._id || user.id || '');
      }

      return { token, user };
    }

    throw new Error('Token não recebido do servidor');
  } catch (error) {
    console.error('Erro no registro:', error.response?.data || error);
    throw error;
  }
};

// Função para fazer logout
export const logout = async () => {
  try {
    // Obter o ID do usuário antes de fazer logout
    let userId = getCurrentUserId();
    console.log(`Iniciando logout para usuário: ${userId}`);

    // Chamar o endpoint de logout no servidor
    const token = getToken();
    if (token) {
      try {
        await axios.post(`/api/logout`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('API de logout chamada com sucesso');
      } catch (apiError) {
        console.error('Erro na API de logout:', apiError);
        // Continuar com logout local mesmo se a API falhar
      }
    }

    // Limpar todos os dados locais usando nossa função aprimorada
    const resultadoLimpeza = limparDadosLocalmente();
    console.log('Resultado da limpeza de dados:', resultadoLimpeza);

    // Forçar recarregamento de telas
    const recarregou = forçaRecarregamentoTelas();
    console.log('Forçou recarregamento das telas?', recarregou);

    // Limpar manualmente dados específicos que podem estar causando problemas
    // Essa é uma limpeza redundante para garantir que tudo seja limpo
    const dadosParaLimparManualmente = [
      // Dados de autenticação
      'auth', 'token', 'user', 'userData',
      // Dados de senhas
      'ultimasSenhasChamadas', 'senhasAguardando', 'senhasChamadas',
      // Dados de painel e estatísticas
      'painelConfig', 'estatisticasCache', 'estatisticasFiltros'
    ];

    console.log('Executando limpeza manual adicional para garantir...');
    dadosParaLimparManualmente.forEach(chave => {
      try {
        localStorage.removeItem(chave);
        localStorage.removeItem(`${chave}_${userId}`);
        localStorage.removeItem(`${chave}_guest`);
        console.log(`Removida manualmente: ${chave}`);
      } catch (e) {
        console.warn(`Erro ao remover item manual ${chave}:`, e);
      }
    });

    // Limpar dados de autenticação por último
    localStorage.removeItem('auth');
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('userData');

    console.log('Logout realizado com sucesso, dados locais limpos');
    return {
      success: true,
      message: 'Logout realizado com sucesso',
      limpeza: resultadoLimpeza,
      recarregou
    };
  } catch (error) {
    console.error('Erro no processo de logout:', error);
    throw error;
  }
};

// Função para criar uma nova senha
export const criarSenha = async (tipo, userId = null) => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    const response = await axios.post(
      `/api/senhas`,
      {
        tipo,
        userId // Incluir o ID do usuário na requisição
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );

    // Verificar se a resposta contém os dados esperados
    if (!response.data || typeof response.data !== 'object' || !response.data._id) {
      console.error('Resposta inválida da API:', response.data);
      throw new Error('Resposta inválida da API ao criar senha');
    }

    // Adicionar propriedade numero para compatibilidade com o código existente
    // Nota: O número formatado será definido no SenhaContext
    if (!response.data.numero && response.data._id) {
      response.data.numero = response.data._id;
    }

    return response.data;
  } catch (error) {
    console.error('Erro ao criar senha:', error);
    throw error;
  }
};

// Função para buscar senhas aguardando
export const buscarSenhasAguardando = async (params = {}) => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    // Configurar os parâmetros da solicitação
    const requestParams = {};

    // Se houver status nos parâmetros, adicioná-los à consulta
    if (params.status) {
      // Se status for um array, enviar como uma lista separada por vírgulas
      if (Array.isArray(params.status)) {
        requestParams.status = params.status.join(',');
      } else {
        requestParams.status = params.status;
      }
    }

    // Incluir o userId nos parâmetros da consulta se disponível
    if (params.userId) {
      requestParams.userId = params.userId;
    }

    console.log('Enviando requisição com parâmetros:', requestParams);

    const response = await axios.get(`/api/senhas/aguardando`, {
      params: requestParams,
      headers: { Authorization: `Bearer ${token}` }
    });

    return response.data;
  } catch (error) {
    console.error('Erro ao buscar senhas aguardando:', error);
    throw error;
  }
};

// Função para atualizar o status de uma senha
export const atualizarStatusSenha = async (senhaId, status, guiche = null, userId = null) => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    // Preparar os dados de atualização
    const updates = { status };

    // Incluir o guichê apenas se fornecido
    if (guiche !== null) {
      updates.guiche = guiche;
    }

    // Incluir timestamps baseados no status
    if (status === 'chamada') {
      updates.horarioChamada = new Date().toISOString();
    } else if (status === 'finalizada') {
      updates.horarioFinalizacao = new Date().toISOString();
    }

    // Incluir o userId se fornecido
    if (userId) {
      updates.userId = userId;
    }

    const response = await axios.put(
      `/api/senhas/${senhaId}`,
      updates,
      { headers: { Authorization: `Bearer ${token}` } }
    );

    return response.data;
  } catch (error) {
    console.error('Erro ao atualizar status da senha:', error);
    throw error;
  }
};

// Função para buscar estatísticas
export const buscarEstatisticas = async (dataInicio, dataFim) => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    const response = await axios.get(`/api/estatisticas`, {
      params: { dataInicio, dataFim },
      headers: { Authorization: `Bearer ${token}` }
    });

    return response.data;
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    throw error;
  }
};

// ==================== FUNÇÕES DE PEDIDOS ====================

// Função para criar um novo pedido
export const criarPedido = async (dadosPedido) => {
  try {
    console.log('[AUTH API] Criando pedido:', dadosPedido);
    console.log('[AUTH API] URL da requisição:', `${axios.defaults.baseURL}/api/pedidos`);

    const response = await axios.post(`/api/pedidos`, dadosPedido);

    console.log('[AUTH API] Pedido criado com sucesso:', response.data);
    return response.data;
  } catch (error) {
    console.error('[AUTH API] Erro ao criar pedido:', error);
    console.error('[AUTH API] Detalhes do erro:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data
      }
    });
    throw error;
  }
};

// Função para buscar pedidos de uma empresa
export const buscarPedidos = async (empresaId) => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    const response = await axios.get(`/api/pedidos`, {
      params: { empresaId },
      headers: { Authorization: `Bearer ${token}` }
    });

    return response.data;
  } catch (error) {
    console.error('Erro ao buscar pedidos:', error);
    throw error;
  }
};

// Função para buscar pedido por ID (público)
export const buscarPedidoPorId = async (pedidoId) => {
  try {
    console.log('Buscando pedido com ID:', pedidoId);

    const response = await axios.get(`/api/pedidos/${pedidoId}`);

    console.log('Pedido encontrado:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar pedido por ID:', error);
    throw error;
  }
};

// Função para atualizar status do pedido
export const atualizarStatusPedido = async (pedidoId, novoStatus, feedback = '') => {
  try {
    const token = getToken();
    if (!token) throw new Error('Usuário não autenticado');

    const response = await axios.put(
      `/api/pedidos/${pedidoId}`,
      {
        status: novoStatus,
        feedback: feedback,
        dataAtualizacao: new Date().toISOString()
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );

    return response.data;
  } catch (error) {
    console.error('Erro ao atualizar status do pedido:', error);
    throw error;
  }
};

// Função para buscar uma senha específica pelo ID (para QR Code)
export const buscarSenhaPorId = async (senhaId) => {
  try {
    console.log('Buscando senha com ID:', senhaId);

    // Validação do ID da senha
    if (!senhaId) {
      throw new Error('ID da senha não fornecido');
    }

    // Limpar o ID da senha para evitar problemas de formatação
    const senhaIdLimpo = String(senhaId).trim().replace(/["']/g, '');
    console.log('ID da senha limpo:', senhaIdLimpo);

    // Verificar conectividade antes de fazer a requisição
    const conectado = await verificarConectividade().catch(() => false);
    if (!conectado) {
      throw new Error('Não foi possível conectar ao servidor. Verifique sua conexão com a internet.');
    }

    // Usar endpoint público que não requer autenticação
    console.log(`Fazendo requisição para /api/senha-publica/${senhaIdLimpo}`);
    const response = await axios.get(`/api/senha-publica/${senhaIdLimpo}`, {
      timeout: 15000, // Aumentado para 15 segundos
      validateStatus: function (status) {
        return status >= 200 && status < 300; // Aceitar apenas status de sucesso
      }
    });

    // Validação da resposta
    if (!response.data) {
      console.error('Resposta vazia do servidor');
      throw new Error('Resposta vazia do servidor ao buscar senha');
    }

    if (!response.data._id) {
      console.error('Resposta sem ID da senha:', response.data);
      throw new Error('Dados da senha inválidos ou incompletos');
    }

    // Verificar se a senha está ativa
    if (response.data.status === 'inativa' || response.data.inativa === true) {
      console.warn('Senha encontrada, mas está inativa:', response.data);
      throw new Error('Esta senha não está mais ativa no sistema. Ela pode ter expirado ou sido cancelada.');
    }

    console.log('Senha encontrada com sucesso:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erro detalhado ao buscar senha por ID:', error);

    // Erros de timeout
    if (error.code === 'ECONNABORTED') {
      throw new Error('O servidor demorou muito para responder. Tente novamente mais tarde.');
    }

    // Erros de resposta HTTP
    if (error.response) {
      console.warn('Resposta de erro do servidor:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });

      switch (error.response.status) {
        case 404:
          throw new Error('Não foi possível encontrar a senha. Verifique o QR code ou se a senha ainda está ativa no sistema.');
        case 400:
          throw new Error('Requisição inválida. O formato do ID da senha pode estar incorreto.');
        case 403:
          throw new Error('Acesso negado. Esta senha pode ter expirado ou não estar mais disponível.');
        case 410:
          throw new Error('Esta senha não está mais disponível. Ela pode ter expirado ou sido finalizada.');
        case 500:
          throw new Error('Erro no servidor. Por favor, tente novamente em alguns instantes.');
        default:
          throw new Error(`Erro ${error.response.status}: ${error.response.data?.message || 'Erro desconhecido'}`);
      }
    }

    // Erros de rede
    if (error.request) {
      console.error('Erro de rede, sem resposta do servidor');
      throw new Error('Não foi possível conectar ao servidor. Verifique sua conexão com a internet e tente novamente.');
    }

    // Mensagem de erro mais específica para o usuário
    if (error.message.includes('inativa') || error.message.includes('expirado')) {
      throw error; // Manter a mensagem original se for sobre senha inativa
    } else {
      throw new Error('Não foi possível encontrar a senha. Verifique o QR code ou se a senha ainda está ativa no sistema.');
    }
  }
};

// Função para buscar senhas aguardando - versão pública
export const buscarSenhasAguardandoPublico = async () => {
  try {
    console.log('Buscando senhas aguardando públicas');
    const response = await axios.get(`/api/senhas-publicas/aguardando`);
    console.log('Senhas aguardando recebidas:', response.data.length);
    return response.data;
  } catch (error) {
    console.error('Erro ao buscar senhas aguardando:', error);
    return [];
  }
};

// Função para limpar dados localmente (sem depender do servidor)
export const limparDadosLocalmente = () => {
  const userId = getCurrentUserId();
  console.log(`Iniciando limpeza local completa para usuário: ${userId}`);

  try {
    // Obter todas as chaves do localStorage
    const todasChaves = [];
    for (let i = 0; i < localStorage.length; i++) {
      todasChaves.push(localStorage.key(i));
    }

    console.log(`Total de ${todasChaves.length} chaves encontradas no localStorage`);

    // Lista de prefixos para limpeza específica - ampliada para capturar mais padrões
    const prefixosParaLimpar = [
      'senhas_',
      'painelConfig',
      'estatisticas',
      'senhasAguardando',
      'display',
      'painel',
      'layout',
      'config',
      'ultimasChamadas_',
      'historicoSenhas_',
      'dadosEstatisticas_',
      'filtrosEstatisticas_',
      'cacheEstatisticas_',
      'painelHistorico_',
      'ultimasSenhas',
      'senhasChamadas',
      'dadosPainel',
      'filtros',
      'cache',
      'historico_',
      'timestamp_',
      'sistema_',
      'stats_'
    ];

    // Lista de chaves exatas para garantir limpeza completa
    const chavesExatas = [
      'senhas_sistema', // Chave usada em PainelSenhas.jsx
      'contadores',
      'ultimaSenhaGerada',
      'painelConfig',
      'displayConfig',
      'layoutConfig',
      'estatisticasConfig',
      'ultimasSenhas',
      'senhasChamadas',
      'estatisticas',
      'senhasAguardando',
      'estatisticas_filtros',
      'estatisticas_cache',
      'display_config',
      'painel_config',
      'timestamp',
      'estatisticas_data',
      'dadosEstatisticas',
      'stats_cache',
      'estatisticasDia',
      'estatisticasMes',
      'estatisticasTotal',
      'servidor_estatisticas',
      'cache_server'
    ];

    // Identificar chaves para limpeza - expansão da lógica para capturar todos os dados
    const chavesParaLimpar = todasChaves.filter(chave =>
      // Chaves específicas do usuário com qualquer prefixo da lista
      (prefixosParaLimpar.some(prefixo => chave.includes(prefixo)) &&
       (chave.includes(userId) || chave.includes('guest')))
      // OU chaves exatas que precisam ser limpas independentemente
      || chavesExatas.includes(chave)
      // OU chaves do sistema sem userId
      || chave === 'painelConfig'
      || chave === 'displayConfig'
      || chave === 'layoutConfig'
      || chave === 'estatisticasConfig'
      || chave.startsWith('painel_')
      || chave.startsWith('display_')
      // Adicionando chaves específicas de estatísticas e senhas
      || chave.startsWith('ultimasSenhas')
      || chave.startsWith('senhasChamadas')
      || chave.startsWith('estatisticas')
      || chave.includes('_estatisticas')
      || chave.includes('_senhas')
      || chave.includes('_painel')
      || chave.includes('senha')
      || chave.includes('display')
      || chave.includes('estatistica')
      || chave.includes('stats')
      || chave.includes('dados')
      || chave.includes('cache')
    );

    console.log(`Removendo ${chavesParaLimpar.length} chaves de configuração, layout, estatísticas e senhas:`);
    console.log(chavesParaLimpar);

    // Remover cada chave
    chavesParaLimpar.forEach(chave => {
      localStorage.removeItem(chave);
      console.log(`Chave removida: ${chave}`);
    });

    // Verificação secundária - buscar chaves residuais com padrões simplificados
    const chavesResiduais = [];
    for (let i = 0; i < localStorage.length; i++) {
      const chave = localStorage.key(i);
      if (chave.includes('senha') ||
          chave.includes('estatistica') ||
          chave.includes('painel') ||
          chave.includes('display') ||
          chave.includes('sistema') ||
          chave.includes('chamada') ||
          chave.includes('stats') ||
          chave.includes('dados') ||
          chave.includes('cache')) {
        chavesResiduais.push(chave);
        localStorage.removeItem(chave);
        console.log(`Chave residual removida: ${chave}`);
      }
    }

    if (chavesResiduais.length > 0) {
      console.log(`Foram encontradas e removidas ${chavesResiduais.length} chaves residuais`);
    }

    // Limpar sessão também (caso haja dados de senhas ou estatísticas)
    try {
      const sessionChaves = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        sessionChaves.push(sessionStorage.key(i));
      }

      // Filtrar chaves da sessão com os mesmos padrões
      const sessionChavesParaLimpar = sessionChaves.filter(chave =>
        prefixosParaLimpar.some(prefixo => chave.includes(prefixo)) ||
        chavesExatas.includes(chave) ||
        chave.includes('estatisticas') ||
        chave.includes('senhas') ||
        chave.includes('painel') ||
        chave.includes('senha') ||
        chave.includes('display') ||
        chave.includes('stats') ||
        chave.includes('dados') ||
        chave.includes('cache')
      );

      console.log(`Removendo ${sessionChavesParaLimpar.length} chaves da sessionStorage:`);

      // Remover cada chave da sessão
      sessionChavesParaLimpar.forEach(chave => {
        sessionStorage.removeItem(chave);
        console.log(`Chave de sessão removida: ${chave}`);
      });
    } catch (sessionError) {
      console.warn('Erro ao limpar sessionStorage:', sessionError);
    }

    // Definir flag específico para estatísticas limpas
    localStorage.setItem('_estatisticas_limpas', 'true');

    return {
      success: true,
      message: `Limpeza local completa concluída. ${chavesParaLimpar.length} itens removidos. ${chavesResiduais.length} itens residuais removidos.`,
      chavesRemovidas: chavesParaLimpar,
      chavesResiduais
    };
  } catch (error) {
    console.error('Erro ao limpar dados localmente:', error);
    return {
      success: false,
      message: `Erro na limpeza local: ${error.message}`,
      error
    };
  }
};

// Função para limpar dados diretamente no servidor Render (usando acesso direto)
export const limparDadosNoServidorDireto = async () => {
  // Verificar conectividade primeiro
  const conectado = await verificarConectividade().catch(() => false);
  console.log('Status de conectividade antes de limpar dados:', conectado ? 'Conectado' : 'Desconectado');
  try {
    console.log('Iniciando processo de limpeza de dados...');

    // Verificar autenticação
    const token = getToken();
    if (!token) {
      throw new Error('Usuário não autenticado');
    }

    // Log do token (parcial, para depuração)
    console.log('Token para autenticação:', token.substring(0, 15) + '...');

    // Extrair userId do token JWT
    let userId = null;
    try {
      // Formato esperado: header.payload.signature
      const tokenParts = token.split('.');
      if (tokenParts.length === 3) {
        // Decodificar a parte payload (índice 1)
        const payload = JSON.parse(atob(tokenParts[1]));
        userId = payload.userId;
        console.log('userId extraído do token:', userId);
      }
    } catch (decodeError) {
      console.error('Erro ao decodificar token:', decodeError);
    }

    if (!userId) {
      // Se não conseguir extrair userId do token, tenta obter do localStorage
      const userInfo = localStorage.getItem('user');
      if (userInfo) {
        try {
          const userObj = JSON.parse(userInfo);
          userId = userObj._id || userObj.id;
          console.log('userId obtido do localStorage:', userId);
        } catch (parseError) {
          console.error('Erro ao analisar usuário do localStorage:', parseError);
        }
      }
    }

    if (!userId) {
      throw new Error('Não foi possível identificar o ID do usuário');
    }

    // Limpar dados locais primeiro para garantir que pelo menos isso funcione
    console.log('Limpando dados locais primeiro...');
    const resultadoLocal = limparDadosLocalmente();

    // Dados comuns para todas as requisições
    const requestData = {
      userId: userId,
      timestamp: new Date().toISOString(),
      action: 'cleanUserData'
    };

    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Lista de endpoints para tentar, em ordem de prioridade
    const endpoints = [
      // Nova rota simplificada - deve ser mais confiável
      {
        url: '/api/limpar-dados-simples',
        method: 'POST',
        headers: authHeaders,
        body: requestData,
        name: 'Rota Simplificada'
      },
      // API de emergência (Pipedream) - mais confiável para contornar problemas de CORS
      {
        url: 'https://eodxvvwqnwxbxzm.m.pipedream.net',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: {
          ...requestData,
          token: token,
          source: 'direct_emergency_clean'
        },
        name: 'API de emergência Pipedream'
      },
      // Endpoint padrão via proxy Vercel
      {
        url: '/api/limpar-dados',
        method: 'POST',
        headers: authHeaders,
        body: requestData,
        name: 'Proxy Vercel'
      },
      // Acesso direto ao Render (pode ter problemas de CORS)
      {
        url: 'https://chamador.onrender.com/api/limpar-dados',
        method: 'POST',
        headers: authHeaders,
        body: requestData,
        name: 'Acesso direto Render'
      },
      // Endpoint de emergência no Render
      {
        url: '/api/limpar-dados-emergencia',
        method: 'GET', // Emergencial usa GET
        headers: authHeaders,
        name: 'Endpoint de emergência Render',
        queryParams: { userId }
      }
    ];

    // Tentar cada endpoint sequencialmente até que um funcione
    for (const endpoint of endpoints) {
      console.log(`Tentando limpar dados usando ${endpoint.name}: ${endpoint.url}`);

      try {
        // Primeiro tentar com fetch normal (sem no-cors)
        const fetchOptions = {
          method: endpoint.method,
          headers: endpoint.headers
        };

        // Adicionar corpo apenas se for POST
        if (endpoint.method === 'POST' && endpoint.body) {
          fetchOptions.body = JSON.stringify(endpoint.body);
        }

        // Construir URL com query params se necessário
        let url = endpoint.url;
        if (endpoint.queryParams) {
          const params = new URLSearchParams();
          for (const [key, value] of Object.entries(endpoint.queryParams)) {
            if (value) {
              params.append(key, value);
            }
          }
          const queryString = params.toString();
          if (queryString) {
            url = `${url}?${queryString}`;
          }
        }

        console.log(`Enviando requisição para ${endpoint.name}`, { url, method: endpoint.method });
        const response = await fetch(url, fetchOptions);

        console.log(`Resposta de ${endpoint.name}:`, {
          status: response.status,
          ok: response.ok,
          statusText: response.statusText
        });

        if (response.ok) {
          let responseData;
          try {
            responseData = await response.json();
            console.log(`Dados da resposta de ${endpoint.name}:`, responseData);
          } catch (jsonError) {
            console.warn(`Erro ao processar JSON da resposta de ${endpoint.name}:`, jsonError);
            responseData = { message: 'Resposta recebida, mas não foi possível processar JSON' };
          }

          console.log(`Limpeza via ${endpoint.name} bem-sucedida:`, responseData);

          return {
            success: true,
            message: `Dados limpos com sucesso via ${endpoint.name}`,
            senhasRemovidas: responseData.senhasRemovidas || 0,
            method: endpoint.name,
            localDataCleaned: true,
            responseData
          };
        } else {
          console.warn(`Resposta não-OK de ${endpoint.name}:`, response.status, response.statusText);

          // Se o erro foi 401 (Unauthorized), o token pode estar inválido
          if (response.status === 401) {
            console.error('Token de autenticação inválido ou expirado. Tente fazer login novamente.');

            // Para os próximos endpoints, não adianta tentar se o token está inválido
            if (endpoint.name === 'Proxy Vercel' || endpoint.name === 'Acesso direto Render') {
              break; // Sai do loop para endpoints de autenticação
            }
          }
        }
      } catch (error) {
        console.warn(`Erro ao usar ${endpoint.name}:`, error.message);
        // Continuar para o próximo endpoint
      }
    }

    // Tentar novamente, agora com no-cors como última tentativa
    console.log('Todas as tentativas normais falharam, tentando com modo no-cors...');

    try {
      const result = await limparDadosNoCorsMode();
      console.log('Resultado da tentativa no-cors:', result);

      return {
        success: true,
        message: 'Dados enviados com modo no-cors, resultado desconhecido',
        method: 'no-cors-mode',
        localDataCleaned: true,
        nocors: true
      };
    } catch (noCorsError) {
      console.error('Erro também no modo no-cors:', noCorsError);
    }

    // Se chegou aqui, todas as tentativas de comunicação com o servidor falharam
    // Mas os dados locais já foram limpos, então retornamos sucesso parcial
    console.log('Todas as tentativas de limpeza no servidor falharam, mas dados locais foram limpos');

    return {
      success: true,
      message: 'Não foi possível limpar dados no servidor, mas dados locais foram limpos com sucesso',
      method: 'local-only',
      localDataCleaned: true,
      senhasRemovidas: 0
    };
  } catch (error) {
    console.error('Erro ao limpar dados no servidor:', error);

    // Tentar limpar dados locais como último recurso
    try {
      console.log('Limpando dados locais após erro...');
      const resultadoLocal = limparDadosLocalmente();

      return {
        success: false,
        message: 'Erro ao limpar dados no servidor, mas dados locais foram limpos',
        error: error.message,
        localDataCleaned: true
      };
    } catch (localError) {
      console.error('Erro ao limpar dados locais:', localError);
    }

    throw error;
  }
};

// Função auxiliar para forçar a reinicialização de componentes após limpeza
function forçaRecarregamentoTelas() {
  console.log('Forçando recarregamento de componentes após limpeza...');

  try {
    // Publicar evento para que componentes possam se atualizar
    const evento = new CustomEvent('dados-limpos', {
      detail: { timestamp: new Date().toISOString() }
    });
    window.dispatchEvent(evento);
    console.log('Evento de recarregamento disparado');

    // Remover dados de cache que podem estar em memória
    // Definir marcadores para componentes verificarem
    localStorage.setItem('_recarregar_estatisticas', new Date().toISOString());
    localStorage.setItem('_recarregar_painel', new Date().toISOString());

    // Esses marcadores serão removidos pelos componentes quando detectarem
    setTimeout(() => {
      try {
        localStorage.removeItem('_recarregar_estatisticas');
        localStorage.removeItem('_recarregar_painel');
      } catch (e) {
        console.error('Erro ao remover marcadores temporários:', e);
      }
    }, 5000); // Remove após 5 segundos se não forem consumidos

    // Forçar recarregamento da página
    window.location.reload();

    return true;
  } catch (e) {
    console.error('Erro ao forçar recarregamento:', e);
    return false;
  }
}

// Função para limpar dados no servidor e localmente (versão melhorada)
export const limparDadosCompletoV2 = async () => {
  // 1. Primeiro limpar dados locais
  console.log('Iniciando limpeza completa V2...');

  // Extrair userId antes de remover os dados de autenticação
  const userId = getCurrentUserId();
  console.log(`ID do usuário para limpeza: ${userId}`);

  // Definir flags para coordenar a limpeza entre componentes
  localStorage.setItem('_limpeza_solicitada', 'true');
  localStorage.setItem('_timestamp_limpeza', Date.now().toString());

  // Limpar dados locais
  const resultadoLocal = limparDadosLocalmente();
  console.log('Resultado da limpeza local:', resultadoLocal);

  // NÃO remover o flag _limpeza_solicitada aqui - deixar para que os componentes o detectem
  // O flag será removido apenas após 5 segundos, tempo suficiente para os componentes reagirem

  // Forçar recarregamento após limpeza local
  const recarregou = forçaRecarregamentoTelas();
  console.log('Forçou recarregamento?', recarregou);

  // 2. Se autenticado, tentar limpar no servidor
  let resultadoServidor = { success: false, message: 'Não autenticado' };

  // Verificar se o usuário está autenticado verificando o token
  const token = getToken();
  if (token) {
    try {
      console.log('Usuário autenticado. Iniciando limpeza no servidor...');
      resultadoServidor = await limparDadosNoServidorDireto();
      console.log('Resultado da limpeza no servidor:', resultadoServidor);
    } catch (error) {
      console.error('Erro na limpeza no servidor:', error);

      // 3. Se falhar, tentar limpeza de emergência
      try {
        console.log('Tentando limpeza de emergência...');
        const resposta = await fetch('https://chamador.onrender.com/api/limpar-dados-emergencia', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        if (resposta.ok) {
          const dados = await resposta.json();
          resultadoServidor = {
            success: true,
            message: 'Limpeza de emergência realizada com sucesso',
            ...dados
          };
        } else {
          resultadoServidor = {
            success: false,
            message: `Erro na limpeza de emergência: ${resposta.statusText}`,
            statusCode: resposta.status
          };
        }
      } catch (emergencyError) {
        resultadoServidor = {
          success: false,
          message: `Falha na limpeza de emergência: ${emergencyError.message}`,
          error: emergencyError
        };
      }
    }
  } else {
    console.log('Usuário não autenticado. Ignorando limpeza no servidor.');
  }

  // 4. Garantir que todas as chaves relacionadas à estatísticas e senhas foram removidas
  // Fazer uma segunda verificação para capturar quaisquer chaves que possam ter sido deixadas
  const verificacaoFinal = [];
  for (let i = 0; i < localStorage.length; i++) {
    const chave = localStorage.key(i);
    if (chave && (
        chave.includes('senha') ||
        chave.includes('estatistica') ||
        chave.includes('painel') ||
        chave.includes('display') ||
        chave.includes('sistema') ||
        chave.includes('chamada') ||
        chave.includes('stats') ||
        chave.includes('dados') ||
        chave.includes('cache'))) {
      verificacaoFinal.push(chave);
      localStorage.removeItem(chave);
      console.log(`Chave removida na verificação final: ${chave}`);
    }
  }

  // 5. Verificação específica para estatísticas - GARANTIR que todas estão limpas
  console.log('Realizando limpeza específica de estatísticas...');

  // Definir flag para indicar que as estatísticas foram limpas
  localStorage.setItem('_estatisticas_limpas', 'true');

  // Limpar explicitamente todas as chaves relacionadas a estatísticas com todos os potenciais formatos
  const userId2 = getCurrentUserId(); // Obter novamente caso tenha sido alterado
  const estatisticasChaves = [
    'estatisticas_cache',
    'estatisticas_filtros',
    'dadosEstatisticas',
    'estatisticas_data',
    'stats_cache',
    'estatisticasDia',
    'estatisticasMes',
    'estatisticasTotal',
    'servidor_estatisticas',
    'cache_server',
    `estatisticas_cache_${userId2}`,
    `estatisticas_filtros_${userId2}`,
    `dadosEstatisticas_${userId2}`,
    `stats_cache_${userId2}`
  ];

  estatisticasChaves.forEach(chave => {
    localStorage.removeItem(chave);
    sessionStorage.removeItem(chave);
    console.log(`Limpeza específica: removida chave de estatísticas ${chave}`);
  });

  // 6. Programar o recarregamento da página com um pequeno atraso
  // Isso garantirá que todos os componentes sejam resetados
  console.log('Programando recarregamento da página para resetar todos os componentes...');

  // Salvar um flag para indicar que a página está sendo recarregada após limpeza
  localStorage.setItem('_limpeza_completa', 'true');

  // Remover o flag _limpeza_solicitada após 5 segundos - tempo suficiente para ser detectado
  setTimeout(() => {
    localStorage.removeItem('_limpeza_solicitada');
    console.log('Flag _limpeza_solicitada removido após timeout');
  }, 5000);

  // Agendar o recarregamento da página após 1 segundo
  setTimeout(() => {
    console.log('Recarregando página após limpeza para garantir reset completo...');
    window.location.reload();
  }, 1000);

  // 7. Combinar resultados
  return {
    success: resultadoLocal.success || resultadoServidor.success,
    local: resultadoLocal,
    servidor: resultadoServidor,
    recarregou,
    chavesVerificacaoFinal: verificacaoFinal,
    message: `Local: ${resultadoLocal.message}. Servidor: ${resultadoServidor.message}. A página será recarregada para garantir reset completo.`
  };
};

/**
 * Tenta limpar dados no servidor usando modo no-cors
 * Essa função é uma solução temporária para contornar problemas de CORS
 * enquanto as configurações no servidor Render não são atualizadas
 *
 * IMPORTANTE: Com mode: 'no-cors', não é possível ler a resposta do servidor
 */
export async function limparDadosNoCorsMode() {
  console.log('Tentando limpeza emergencial com modo no-cors...');

  try {
    // Obter userId
    let userId = null;
    try {
      const authData = localStorage.getItem('auth');
      if (authData) {
        const auth = JSON.parse(authData);
        const tokenParts = auth.token.split('.');
        const payload = JSON.parse(atob(tokenParts[1]));
        userId = payload.userId;
        console.log('userId para limpeza emergencial:', userId);
      }
    } catch (e) {
      console.error('Erro ao extrair userId:', e);
    }

    // Construir URL com userId como parâmetro
    const emergencyUrl = userId
      ? `/api/limpar-dados-emergencia?userId=${encodeURIComponent(userId)}`
      : '/api/limpar-dados-emergencia';

    // Primeiro tenta usando a rota proxy no Vercel com parâmetro userId
    console.log('Tentando via proxy Vercel com URL:', emergencyUrl);

    const proxyResponse = await fetch(emergencyUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (proxyResponse.ok) {
      const data = await proxyResponse.json();
      console.log('Resposta da limpeza emergencial:', data);
      return { success: true, data };
    }

    console.log('Requisição via proxy enviada, status:', proxyResponse.status);

    // Se o proxy falhar, tenta diretamente no Render como fallback
    setTimeout(async () => {
      try {
        console.log('Tentando diretamente no Render...');
        const renderUrl = userId
          ? `https://chamador.onrender.com/api/limpar-dados-emergencia?userId=${encodeURIComponent(userId)}`
          : 'https://chamador.onrender.com/api/limpar-dados-emergencia';

        const response = await fetch(renderUrl, {
          method: 'GET',
          mode: 'no-cors',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        console.log('Requisição direta enviada');
      } catch (directError) {
        console.error('Erro na requisição direta:', directError);
      }
    }, 1000);

    return { success: true, message: 'Requisições de limpeza enviadas (no-cors mode)' };
  } catch (error) {
    console.error('Erro ao tentar limpeza no-cors:', error);
    return { success: false, message: 'Falha nas requisições - Versão atualizada', error };
  }
}

/**
 * Função de diagnóstico para verificar detalhes do token
 * Útil para depurar problemas de autenticação
 */
export function verificarToken() {
  console.log('---- DIAGNÓSTICO DE AUTENTICAÇÃO ----');

  // Verificar se existe dados de autenticação
  const authData = localStorage.getItem('auth');
  console.log('Auth data existe?', !!authData);

  if (!authData) {
    console.log('Não há dados de autenticação no localStorage.');
    return {
      autenticado: false,
      mensagem: 'Não há dados de autenticação'
    };
  }

  try {
    // Tentar fazer parse dos dados
    const auth = JSON.parse(authData);
    console.log('Dados de autenticação:', {
      temToken: !!auth.token,
      userId: auth.userId || 'não disponível',
      tokenComprimento: auth.token ? auth.token.length : 0,
      // Mostrar apenas os primeiros e últimos 10 caracteres do token para segurança
      tokenPreview: auth.token ? `${auth.token.substring(0, 10)}...${auth.token.substring(auth.token.length - 10)}` : 'não disponível'
    });

    // Verificar validade do token (decodificar sem verificar assinatura)
    if (auth.token) {
      try {
        const tokenParts = auth.token.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(atob(tokenParts[1]));
          console.log('Payload do token:', {
            exp: payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'não disponível',
            iat: payload.iat ? new Date(payload.iat * 1000).toLocaleString() : 'não disponível',
            expirado: payload.exp ? Date.now() >= payload.exp * 1000 : 'não verificável'
          });

          return {
            autenticado: true,
            expirado: payload.exp ? Date.now() >= payload.exp * 1000 : null,
            expiraEm: payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'desconhecido',
            userId: auth.userId
          };
        } else {
          console.log('Token não parece estar no formato JWT válido (header.payload.signature)');
        }
      } catch (decodeError) {
        console.error('Erro ao decodificar token:', decodeError);
      }
    }

    return {
      autenticado: !!auth.token,
      tokenValido: auth.token && auth.token.split('.').length === 3,
      userId: auth.userId
    };
  } catch (error) {
    console.error('Erro ao analisar dados de autenticação:', error);
    return {
      autenticado: false,
      erro: error.message,
      mensagem: 'Erro ao processar dados de autenticação'
    };
  }
}

/**
 * Função para testar a resposta do servidor e exibir informações
 * Útil para diagnosticar problemas de CORS e comunicação
 */
export async function testarConexaoServidor() {
  console.log('---- TESTE DE CONEXÃO COM SERVIDOR ----');

  try {
    // Teste via proxy Vercel
    console.log('Testando via proxy Vercel...');
    const proxyResponse = await fetch('/api/check-connection');
    const proxyData = await proxyResponse.json();
    console.log('Resposta do proxy:', proxyData);

    // Testar CORS
    console.log('Testando CORS...');
    const corsResponse = await fetch('/api/cors-test');
    const corsData = await corsResponse.json();
    console.log('Dados CORS:', corsData);

    return {
      success: true,
      proxyFuncionando: proxyResponse.ok,
      corsFuncionando: corsResponse.ok,
      proxyData,
      corsData
    };
  } catch (error) {
    console.error('Erro ao testar conexão:', error);
    return {
      success: false,
      erro: error.message
    };
  }
}

/**
 * Função para diagnosticar problemas na limpeza de dados
 * Testa diferentes métodos e mostra resultados detalhados
 */
export async function diagnosticarLimpezaDados() {
  console.log('---- DIAGNÓSTICO DE LIMPEZA DE DADOS ----');

  // Verificar autenticação primeiro
  const tokenInfo = verificarToken();
  console.log('Status de autenticação:', tokenInfo);

  if (!tokenInfo.autenticado) {
    console.log('Usuário não autenticado. A limpeza de dados requer autenticação.');
    return {
      success: false,
      mensagem: 'Usuário não autenticado',
      solucao: 'Faça login novamente para obter um token válido'
    };
  }

  if (tokenInfo.expirado) {
    console.log('Token expirado. A limpeza de dados requer um token válido.');
    return {
      success: false,
      mensagem: 'Token expirado',
      solucao: 'Faça login novamente para obter um token válido',
      expiradoEm: tokenInfo.expiraEm
    };
  }

  // Extrair token para testes
  const authData = JSON.parse(localStorage.getItem('auth'));
  const token = authData.token;

  // Testar requisição sem usar o frontend (diretamente com fetch)
  try {
    console.log('Testando limpeza via proxy com fetch direto...');

    // Primeiro, testar a rota simplificada
    console.log('1. Testando rota simplificada...');
    try {
      const responseSimples = await fetch('/api/limpar-dados-simples', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Status da resposta (simplificada):', responseSimples.status);
      const dataSimples = await responseSimples.json().catch(e => ({ erro: 'Falha ao parsear JSON' }));
      console.log('Dados da resposta (simplificada):', dataSimples);

      if (responseSimples.ok) {
        return {
          success: true,
          statusCode: responseSimples.status,
          dados: dataSimples,
          message: 'Limpeza via rota simplificada realizada com sucesso'
        };
      }
    } catch (simplesError) {
      console.error('Erro ao usar rota simplificada:', simplesError);
    }

    // Se a rota simplificada falhar, testar a rota normal
    console.log('2. Testando rota padrão...');
    const response = await fetch('/api/limpar-dados', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Status da resposta:', response.status);
    console.log('Headers da resposta:', {
      'content-type': response.headers.get('content-type'),
      'access-control-allow-origin': response.headers.get('access-control-allow-origin')
    });

    const data = await response.json().catch(e => ({ erro: 'Falha ao parsear JSON' }));
    console.log('Dados da resposta:', data);

    // Testar com modo no-cors para ver se é problema de CORS
    console.log('Testando com modo no-cors...');
    await limparDadosNoCorsMode();

    return {
      success: response.ok,
      statusCode: response.status,
      dados: data,
      headers: {
        'content-type': response.headers.get('content-type'),
        'access-control-allow-origin': response.headers.get('access-control-allow-origin')
      }
    };
  } catch (error) {
    console.error('Erro no teste de limpeza:', error);

    // Se houve erro, tentar no-cors como último recurso
    console.log('Tentando com no-cors após erro...');
    try {
      await limparDadosNoCorsMode();
      console.log('Requisição no-cors enviada');
    } catch (noCorsError) {
      console.error('Erro também no modo no-cors:', noCorsError);
    }

    return {
      success: false,
      erro: error.message,
      tipo: error.name,
      stack: error.stack,
      mensagem: 'Erro ao testar limpeza de dados'
    };
  }
}

// Função auxiliar para obter o ID do usuário atual
function getCurrentUserId() {
  try {
    const userData = localStorage.getItem('userData');
    if (userData) {
      const user = JSON.parse(userData);
      return user._id || 'guest';
    }

    // Tenta buscar do objeto auth se userData não existir
    const authData = localStorage.getItem('auth');
    if (authData) {
      const auth = JSON.parse(authData);
      return auth.userId || 'guest';
    }
  } catch (error) {
    console.error('Erro ao obter ID do usuário:', error);
  }
  return 'guest';
}

/**
 * Notas sobre as melhorias na limpeza de dados:
 *
 * 1. Ampliado o conjunto de chaves verificadas na função limparDadosLocalmente
 *    - Adicionadas mais chaves específicas relacionadas a estatísticas
 *    - Melhorada a detecção de padrões para capturar variações de nomes
 *
 * 2. Adicionado flag '_estatisticas_limpas' para garantir que componentes
 *    de estatísticas saibam que um processo de limpeza ocorreu
 *
 * 3. Implementada verificação secundária específica para estatísticas na função
 *    limparDadosCompletoV2 para garantir que todos os dados são removidos
 *
 * 4. Adicionada limpeza mais agressiva que inclui chaves relacionadas a:
 *    - Estatísticas (estatistica, stats)
 *    - Dados (dados)
 *    - Cache (cache)
 *    - Combinações de userId com estas chaves
 *
 * Estes aprimoramentos garantem que nenhum dado persista nas estatísticas
 * após o processo de limpeza, mesmo quando houver interação com o servidor.
 */