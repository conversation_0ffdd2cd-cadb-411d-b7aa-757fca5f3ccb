import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Tabs, Tab, Box, TextField, Slider, FormControl, InputLabel, Select, MenuItem, Switch, FormControlLabel, Typography, Button, Grid, Paper } from '@mui/material';
import ColorPicker from './ColorPicker';
import layoutThemes from '../themes/layoutThemes';

const CustomizerContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`;

const PreviewContainer = styled.div`
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  min-height: 300px;
  background-color: ${props => props.backgroundColor || '#f8f9fa'};
  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
`;

const PreviewHeader = styled.div`
  background: ${props => props.headerColor || 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)'};
  color: ${props => props.headerTextColor || 'white'};
  padding: 10px;
  text-align: center;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
`;

const PreviewTitle = styled.h3`
  margin: 0;
  font-family: ${props => props.fontFamily || 'Arial'};
  font-size: 18px;
`;

const PreviewContent = styled.div`
  display: ${props => props.display || 'flex'};
  flex-direction: ${props => props.flexDirection || 'row'};
  gap: ${props => props.gap || '10px'};
  flex: 1;
  grid-template-columns: ${props => props.gridTemplateColumns || 'none'};
  grid-template-rows: ${props => props.gridTemplateRows || 'none'};
`;

const PreviewSenhaAtual = styled.div`
  flex: ${props => props.flex || '2'};
  background-color: ${props => props.backgroundColor || 'white'};
  border-radius: ${props => props.borderRadius || '8px'};
  box-shadow: ${props => props.boxShadow || '0 2px 8px rgba(0, 0, 0, 0.1)'};
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: ${props => props.border || 'none'};
  grid-column: ${props => props.gridColumn || 'auto'};
  grid-row: ${props => props.gridRow || 'auto'};
`;

const PreviewSenhaNumero = styled.div`
  font-size: ${props => props.fontSize || '36px'};
  font-weight: bold;
  color: ${props => props.color || '#3498db'};
  font-family: ${props => props.fontFamily || 'Arial'};
`;

const PreviewSenhasList = styled.div`
  flex: ${props => props.flex || '1'};
  background-color: ${props => props.backgroundColor || 'white'};
  border-radius: ${props => props.borderRadius || '8px'};
  box-shadow: ${props => props.boxShadow || '0 2px 8px rgba(0, 0, 0, 0.1)'};
  padding: 15px;
  display: flex;
  flex-direction: column;
  grid-column: ${props => props.gridColumn || 'auto'};
  grid-row: ${props => props.gridRow || 'auto'};
`;

const PreviewFooter = styled.div`
  background: ${props => props.footerColor || 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)'};
  color: ${props => props.footerTextColor || 'white'};
  padding: 8px;
  text-align: center;
  border-radius: 4px;
  margin-top: 10px;
  font-family: ${props => props.fontFamily || 'Arial'};
  font-size: 14px;
`;

const JsonEditor = styled.textarea`
  width: 100%;
  height: 200px;
  font-family: monospace;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-bottom: 10px;
`;

const CssEditor = styled.textarea`
  width: 100%;
  height: 150px;
  font-family: monospace;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-bottom: 10px;
`;

const PainelCustomizer = ({ config, onChange, onSave }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [customConfig, setCustomConfig] = useState(config);
  const [jsonConfig, setJsonConfig] = useState('');
  const [customCss, setCustomCss] = useState('');
  const [jsonError, setJsonError] = useState(false);

  useEffect(() => {
    setCustomConfig(config);
    setJsonConfig(JSON.stringify(config, null, 2));
  }, [config]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleConfigChange = (path, value) => {
    const newConfig = { ...customConfig };
    
    // Handle nested paths like 'colors.bg'
    if (path.includes('.')) {
      const parts = path.split('.');
      let current = newConfig;
      
      for (let i = 0; i < parts.length - 1; i++) {
        if (!current[parts[i]]) {
          current[parts[i]] = {};
        }
        current = current[parts[i]];
      }
      
      current[parts[parts.length - 1]] = value;
    } else {
      newConfig[path] = value;
    }
    
    setCustomConfig(newConfig);
    onChange(newConfig);
  };

  const handleJsonChange = (e) => {
    setJsonConfig(e.target.value);
    try {
      const parsed = JSON.parse(e.target.value);
      setCustomConfig(parsed);
      onChange(parsed);
      setJsonError(false);
    } catch (error) {
      setJsonError(true);
    }
  };

  const handleCssChange = (e) => {
    setCustomCss(e.target.value);
    // Aqui você pode implementar a lógica para aplicar o CSS personalizado
  };

  const handleSave = () => {
    onSave(customConfig);
  };

  const handleReset = () => {
    setCustomConfig(config);
    setJsonConfig(JSON.stringify(config, null, 2));
    onChange(config);
  };

  // Opções para posicionamento do logo
  const logoPositions = [
    { value: 'left', label: 'Esquerda' },
    { value: 'center', label: 'Centro' },
    { value: 'right', label: 'Direita' }
  ];

  // Opções para tamanho do logo
  const logoSizes = [
    { value: 'small', label: 'Pequeno' },
    { value: 'medium', label: 'Médio' },
    { value: 'large', label: 'Grande' }
  ];

  // Opções para layout do painel
  const layoutOptions = [
    { value: 'flex', label: 'Flexbox' },
    { value: 'grid', label: 'Grid' }
  ];

  // Opções para bordas
  const borderStyles = [
    { value: 'none', label: 'Nenhuma' },
    { value: 'solid', label: 'Sólida' },
    { value: 'dashed', label: 'Tracejada' },
    { value: 'dotted', label: 'Pontilhada' },
    { value: 'double', label: 'Dupla' }
  ];

  return (
    <CustomizerContainer>
      <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
        <Tab label="Cores" />
        <Tab label="Layout" />
        <Tab label="Componentes" />
        <Tab label="Avançado" />
      </Tabs>

      {/* Aba de Cores */}
      {activeTab === 0 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>Cores do Painel</Typography>
          
          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Fundo Principal</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.backgroundColor || '#f8f9fa'} 
                onChange={(color) => handleConfigChange('backgroundColor', color)} 
              />
            </Grid>
          </Grid>

          <Typography variant="h6" gutterBottom>Cores dos Componentes</Typography>
          
          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Cabeçalho</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.headerColor || '#2c3e50'} 
                onChange={(color) => handleConfigChange('headerColor', color)} 
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Texto do Cabeçalho</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.headerTextColor || '#ffffff'} 
                onChange={(color) => handleConfigChange('headerTextColor', color)} 
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Senha Atual</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.senhaAtualBgColor || '#ffffff'} 
                onChange={(color) => handleConfigChange('senhaAtualBgColor', color)} 
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Número da Senha</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.senhaColor || '#3498db'} 
                onChange={(color) => handleConfigChange('senhaColor', color)} 
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Lista de Senhas</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.senhasListBgColor || '#ffffff'} 
                onChange={(color) => handleConfigChange('senhasListBgColor', color)} 
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Rodapé</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.footerColor || '#2c3e50'} 
                onChange={(color) => handleConfigChange('footerColor', color)} 
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Typography>Texto do Rodapé</Typography>
            </Grid>
            <Grid item xs={8}>
              <ColorPicker 
                color={customConfig.footerTextColor || '#ffffff'} 
                onChange={(color) => handleConfigChange('footerTextColor', color)} 
              />
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Aba de Layout */}
      {activeTab === 1 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>Tipo de Layout</Typography>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Tipo de Layout</InputLabel>
            <Select
              value={customConfig.layoutType || 'flex'}
              onChange={(e) => handleConfigChange('layoutType', e.target.value)}
              label="Tipo de Layout"
            >
              {layoutOptions.map(option => (
                <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
              ))}
            </Select>
          </FormControl>

          {customConfig.layoutType === 'flex' ? (
            <>
              <Typography variant="h6" gutterBottom>Configurações Flexbox</Typography>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Direção</InputLabel>
                <Select
                  value={customConfig.flexDirection || 'row'}
                  onChange={(e) => handleConfigChange('flexDirection', e.target.value)}
                  label="Direção"
                >
                  <MenuItem value="row">Horizontal</MenuItem>
                  <MenuItem value="column">Vertical</MenuItem>
                </Select>
              </FormControl>

              <Typography gutterBottom>Espaçamento entre componentes</Typography>
              <Slider
                value={parseInt(customConfig.gap) || 20}
                onChange={(e, newValue) => handleConfigChange('gap', `${newValue}px`)}
                min={0}
                max={50}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />

              <Typography gutterBottom>Proporção Senha Atual</Typography>
              <Slider
                value={parseInt(customConfig.senhaAtualFlex) || 2}
                onChange={(e, newValue) => handleConfigChange('senhaAtualFlex', newValue)}
                min={1}
                max={5}
                step={0.5}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />

              <Typography gutterBottom>Proporção Lista de Senhas</Typography>
              <Slider
                value={parseInt(customConfig.senhasListFlex) || 1}
                onChange={(e, newValue) => handleConfigChange('senhasListFlex', newValue)}
                min={1}
                max={5}
                step={0.5}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
            </>
          ) : (
            <>
              <Typography variant="h6" gutterBottom>Configurações Grid</Typography>
              
              <TextField
                fullWidth
                label="Colunas do Grid"
                value={customConfig.gridTemplateColumns || '2fr 1fr'}
                onChange={(e) => handleConfigChange('gridTemplateColumns', e.target.value)}
                helperText="Ex: 2fr 1fr, 1fr 1fr 1fr"
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Linhas do Grid"
                value={customConfig.gridTemplateRows || 'auto auto'}
                onChange={(e) => handleConfigChange('gridTemplateRows', e.target.value)}
                helperText="Ex: auto auto, 1fr 2fr"
                sx={{ mb: 2 }}
              />

              <Typography gutterBottom>Espaçamento entre componentes</Typography>
              <Slider
                value={parseInt(customConfig.gap) || 20}
                onChange={(e, newValue) => handleConfigChange('gap', `${newValue}px`)}
                min={0}
                max={50}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Posição da Senha Atual (Grid)"
                value={customConfig.senhaAtualGridPosition || '1 / 1 / 3 / 2'}
                onChange={(e) => handleConfigChange('senhaAtualGridPosition', e.target.value)}
                helperText="Formato: linha-início / coluna-início / linha-fim / coluna-fim"
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Posição da Lista de Senhas (Grid)"
                value={customConfig.senhasListGridPosition || '1 / 2 / 3 / 3'}
                onChange={(e) => handleConfigChange('senhasListGridPosition', e.target.value)}
                helperText="Formato: linha-início / coluna-início / linha-fim / coluna-fim"
                sx={{ mb: 2 }}
              />
            </>
          )}
        </Box>
      )}

      {/* Aba de Componentes */}
      {activeTab === 2 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>Senha Atual</Typography>
          
          <FormControlLabel
            control={
              <Switch
                checked={customConfig.showSenhaAtual !== false}
                onChange={(e) => handleConfigChange('showSenhaAtual', e.target.checked)}
              />
            }
            label="Mostrar Senha Atual"
            sx={{ mb: 2 }}
          />

          <Typography gutterBottom>Tamanho da Fonte</Typography>
          <Slider
            value={parseInt(customConfig.fontSize) || 120}
            onChange={(e, newValue) => handleConfigChange('fontSize', newValue)}
            min={60}
            max={200}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Família da Fonte</InputLabel>
            <Select
              value={customConfig.fontFamily || 'Arial'}
              onChange={(e) => handleConfigChange('fontFamily', e.target.value)}
              label="Família da Fonte"
            >
              <MenuItem value="Arial">Arial</MenuItem>
              <MenuItem value="Helvetica">Helvetica</MenuItem>
              <MenuItem value="Verdana">Verdana</MenuItem>
              <MenuItem value="Times New Roman">Times New Roman</MenuItem>
              <MenuItem value="Courier New">Courier New</MenuItem>
              <MenuItem value="Georgia">Georgia</MenuItem>
              <MenuItem value="Tahoma">Tahoma</MenuItem>
              <MenuItem value="Trebuchet MS">Trebuchet MS</MenuItem>
            </Select>
          </FormControl>

          <Typography gutterBottom>Arredondamento das Bordas</Typography>
          <Slider
            value={parseInt(customConfig.borderRadius) || 12}
            onChange={(e, newValue) => handleConfigChange('borderRadius', `${newValue}px`)}
            min={0}
            max={30}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Estilo da Borda</InputLabel>
            <Select
              value={customConfig.borderStyle || 'none'}
              onChange={(e) => handleConfigChange('borderStyle', e.target.value)}
              label="Estilo da Borda"
            >
              {borderStyles.map(style => (
                <MenuItem key={style.value} value={style.value}>{style.label}</MenuItem>
              ))}
            </Select>
          </FormControl>

          {customConfig.borderStyle !== 'none' && (
            <>
              <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
                <Grid item xs={4}>
                  <Typography>Cor da Borda</Typography>
                </Grid>
                <Grid item xs={8}>
                  <ColorPicker 
                    color={customConfig.borderColor || '#ddd'} 
                    onChange={(color) => handleConfigChange('borderColor', color)} 
                  />
                </Grid>
              </Grid>

              <Typography gutterBottom>Espessura da Borda</Typography>
              <Slider
                value={parseInt(customConfig.borderWidth) || 1}
                onChange={(e, newValue) => handleConfigChange('borderWidth', `${newValue}px`)}
                min={1}
                max={10}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
            </>
          )}

          <Typography gutterBottom>Intensidade da Sombra</Typography>
          <Slider
            value={parseInt(customConfig.shadowIntensity) || 15}
            onChange={(e, newValue) => handleConfigChange('shadowIntensity', newValue)}
            min={0}
            max={30}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />

          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>Logo</Typography>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Posição do Logo</InputLabel>
            <Select
              value={customConfig.logoPosition || 'center'}
              onChange={(e) => handleConfigChange('logoPosition', e.target.value)}
              label="Posição do Logo"
            >
              {logoPositions.map(position => (
                <MenuItem key={position.value} value={position.value}>{position.label}</MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Tamanho do Logo</InputLabel>
            <Select
              value={customConfig.logoSize || 'medium'}
              onChange={(e) => handleConfigChange('logoSize', e.target.value)}
              label="Tamanho do Logo"
            >
              {logoSizes.map(size => (
                <MenuItem key={size.value} value={size.value}>{size.label}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      {/* Aba Avançado */}
      {activeTab === 3 && (
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>Editor JSON</Typography>
          <JsonEditor 
            value={jsonConfig} 
            onChange={handleJsonChange} 
            error={jsonError}
          />
          {jsonError && (
            <Typography color="error" variant="caption" sx={{ mb: 2, display: 'block' }}>
              JSON inválido. Verifique a sintaxe.
            </Typography>
          )}

          <Typography variant="h6" gutterBottom>CSS Personalizado</Typography>
          <CssEditor 
            value={customCss} 
            onChange={handleCssChange} 
            placeholder="/* Adicione seu CSS personalizado aqui */"
          />
          <Typography variant="caption" sx={{ mb: 2, display: 'block' }}>
            O CSS personalizado será aplicado ao painel de senhas.
          </Typography>
        </Box>
      )}

      {/* Prévia */}
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>Prévia</Typography>
      <PreviewContainer 
        backgroundColor={customConfig.backgroundColor}
        backgroundImage={customConfig.backgroundImage}
      >
        <PreviewHeader 
          headerColor={customConfig.headerColor}
          headerTextColor={customConfig.headerTextColor}
        >
          <PreviewTitle fontFamily={customConfig.fontFamily}>Painel de Senhas</PreviewTitle>
        </PreviewHeader>
        
        <PreviewContent 
          display={customConfig.layoutType === 'grid' ? 'grid' : 'flex'}
          flexDirection={customConfig.flexDirection || 'row'}
          gap={customConfig.gap || '10px'}
          gridTemplateColumns={customConfig.gridTemplateColumns}
          gridTemplateRows={customConfig.gridTemplateRows}
        >
          {customConfig.showSenhaAtual !== false && (
            <PreviewSenhaAtual 
              flex={customConfig.senhaAtualFlex || 2}
              backgroundColor={customConfig.senhaAtualBgColor || 'white'}
              borderRadius={customConfig.borderRadius || '8px'}
              boxShadow={customConfig.shadowIntensity ? `0 2px ${customConfig.shadowIntensity}px rgba(0, 0, 0, 0.1)` : '0 2px 8px rgba(0, 0, 0, 0.1)'}
              border={customConfig.borderStyle !== 'none' ? `${customConfig.borderWidth || '1px'} ${customConfig.borderStyle} ${customConfig.borderColor || '#ddd'}` : 'none'}
              gridColumn={customConfig.layoutType === 'grid' ? customConfig.senhaAtualGridPosition?.split('/')[1] : 'auto'}
              gridRow={customConfig.layoutType === 'grid' ? customConfig.senhaAtualGridPosition?.split('/')[0] : 'auto'}
            >
              <Typography variant="subtitle1" sx={{ mb: 1 }}>Senha Atual</Typography>
              <PreviewSenhaNumero 
                fontSize={`${Math.min(customConfig.fontSize || 36, 48)}px`}
                color={customConfig.senhaColor || '#3498db'}
                fontFamily={customConfig.fontFamily || 'Arial'}
              >
                N42
              </PreviewSenhaNumero>
              <Typography variant="body2" sx={{ mt: 1 }}>Guichê: 3</Typography>
            </PreviewSenhaAtual>
          )}
          
          <PreviewSenhasList 
            flex={customConfig.senhasListFlex || 1}
            backgroundColor={customConfig.senhasListBgColor || 'white'}
            borderRadius={customConfig.borderRadius || '8px'}
            boxShadow={customConfig.shadowIntensity ? `0 2px ${customConfig.shadowIntensity}px rgba(0, 0, 0, 0.1)` : '0 2px 8px rgba(0, 0, 0, 0.1)'}
            border={customConfig.borderStyle !== 'none' ? `${customConfig.borderWidth || '1px'} ${customConfig.borderStyle} ${customConfig.borderColor || '#ddd'}` : 'none'}
            gridColumn={customConfig.layoutType === 'grid' ? customConfig.senhasListGridPosition?.split('/')[1] : 'auto'}
            gridRow={customConfig.layoutType === 'grid' ? customConfig.senhasListGridPosition?.split('/')[0] : 'auto'}
          >
            <Typography variant="subtitle1" sx={{ mb: 1 }}>Últimas Senhas</Typography>
            <Paper elevation={1} sx={{ p: 1, mb: 1 }}>
              <Typography variant="body2">N41 - Guichê 2</Typography>
            </Paper>
            <Paper elevation={1} sx={{ p: 1, mb: 1 }}>
              <Typography variant="body2">P12 - Guichê 1</Typography>
            </Paper>
          </PreviewSenhasList>
        </PreviewContent>
        
        <PreviewFooter 
          footerColor={customConfig.footerColor}
          footerTextColor={customConfig.footerTextColor}
          fontFamily={customConfig.fontFamily}
        >
          Sistema de Gerenciamento de Senhas
        </PreviewFooter>
      </PreviewContainer>

      {/* Botões de ação */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, gap: 2 }}>
        <Button variant="outlined" onClick={handleReset}>Resetar</Button>
        <Button variant="contained" onClick={handleSave}>Salvar Configurações</Button>
      </Box>
    </CustomizerContainer>
  );
};

export default PainelCustomizer;