import React, { useState } from 'react';
import styled from 'styled-components';
import { FaCheck, FaTimes, FaSave, FaUser, FaCog, FaChartBar, FaPalette, FaShoppingCart, FaTrash, FaDownload, FaVolumeUp, FaEye, FaEdit } from 'react-icons/fa';

const PermissionsContainer = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
`;

const Title = styled.h3`
  color: #2c3e50;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const PermissionSection = styled.div`
  margin-bottom: 24px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
`;

const SectionTitle = styled.h4`
  color: #495057;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
`;

const PermissionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
`;

const PermissionItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: ${props => props.$enabled ? '#f8f9fa' : '#fff5f5'};
  border: 1px solid ${props => props.$enabled ? '#28a745' : '#dc3545'};
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const PermissionLabel = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const PermissionName = styled.span`
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
`;

const PermissionDescription = styled.span`
  font-size: 12px;
  color: #6c757d;
  margin-top: 2px;
`;

const ToggleButton = styled.button`
  background: ${props => props.$enabled ? '#28a745' : '#dc3545'};
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
`;

const SaveButton = styled.button`
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 20px;
  transition: all 0.3s ease;

  &:hover {
    background: #0056b3;
  }

  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }
`;

const PermissionsManager = ({ user, onSave, loading }) => {
  const [permissions, setPermissions] = useState(user?.permissoes || {});

  const permissionCategories = {
    principais: {
      title: 'Funcionalidades Principais',
      icon: <FaUser />,
      permissions: {
        gerarSenha: {
          name: 'Gerar Senha',
          description: 'Permite gerar novas senhas para atendimento'
        },
        chamarSenha: {
          name: 'Chamar Senha',
          description: 'Permite chamar senhas para atendimento'
        },
        finalizarSenha: {
          name: 'Finalizar Senha',
          description: 'Permite finalizar o atendimento de senhas'
        }
      }
    },
    paginas: {
      title: 'Acesso às Páginas',
      icon: <FaEye />,
      permissions: {
        acessarPainel: {
          name: 'Painel de Senhas',
          description: 'Acesso à página do painel de senhas'
        },
        acessarEstatisticas: {
          name: 'Estatísticas',
          description: 'Acesso à página de estatísticas e relatórios'
        },
        acessarConfiguracao: {
          name: 'Configurações',
          description: 'Acesso às configurações do sistema'
        },
        acessarDesign: {
          name: 'Design',
          description: 'Acesso às configurações de design e layout'
        }
      }
    },
    pedidos: {
      title: 'Sistema de Pedidos',
      icon: <FaShoppingCart />,
      permissions: {
        gerenciarPedidos: {
          name: 'Gerenciar Pedidos',
          description: 'Visualizar e gerenciar pedidos dos clientes'
        },
        criarQRCodePedidos: {
          name: 'QR Code de Pedidos',
          description: 'Gerar QR Codes para pedidos via celular'
        }
      }
    },
    avancadas: {
      title: 'Funcionalidades Avançadas',
      icon: <FaCog />,
      permissions: {
        limparDados: {
          name: 'Limpar Dados',
          description: 'Permite limpar dados do sistema'
        },
        exportarDados: {
          name: 'Exportar Dados',
          description: 'Exportar relatórios e dados do sistema'
        },
        configurarTiposSenha: {
          name: 'Tipos de Senha',
          description: 'Configurar tipos personalizados de senha'
        },
        configurarSom: {
          name: 'Configurações de Som',
          description: 'Configurar sons e voz do sistema'
        },
        configurarLayout: {
          name: 'Layout Personalizado',
          description: 'Personalizar layout e aparência'
        }
      }
    },
    publico: {
      title: 'Acesso Público',
      icon: <FaEye />,
      permissions: {
        permitirAcessoPublico: {
          name: 'Acesso Público',
          description: 'Permitir acesso público ao painel de senhas'
        }
      }
    },
    empresa: {
      title: 'Configurações da Empresa',
      icon: <FaEdit />,
      permissions: {
        editarDadosEmpresa: {
          name: 'Editar Dados',
          description: 'Editar nome da empresa e informações'
        },
        alterarSenha: {
          name: 'Alterar Senha',
          description: 'Alterar senha de acesso ao sistema'
        }
      }
    }
  };

  const togglePermission = (permissionKey) => {
    setPermissions(prev => ({
      ...prev,
      [permissionKey]: !prev[permissionKey]
    }));
  };

  const handleSave = () => {
    onSave(permissions);
  };

  const getCategoryIcon = (category) => {
    const icons = {
      principais: <FaUser />,
      paginas: <FaEye />,
      pedidos: <FaShoppingCart />,
      avancadas: <FaCog />,
      publico: <FaEye />,
      empresa: <FaEdit />
    };
    return icons[category] || <FaCog />;
  };

  return (
    <PermissionsContainer>
      <Title>
        <FaCog />
        Gerenciar Permissões - {user?.companyName}
      </Title>

      {Object.entries(permissionCategories).map(([categoryKey, category]) => (
        <PermissionSection key={categoryKey}>
          <SectionTitle>
            {getCategoryIcon(categoryKey)}
            {category.title}
          </SectionTitle>
          
          <PermissionGrid>
            {Object.entries(category.permissions).map(([permKey, permission]) => (
              <PermissionItem 
                key={permKey} 
                $enabled={permissions[permKey]}
              >
                <PermissionLabel>
                  <PermissionName>{permission.name}</PermissionName>
                  <PermissionDescription>{permission.description}</PermissionDescription>
                </PermissionLabel>
                
                <ToggleButton
                  $enabled={permissions[permKey]}
                  onClick={() => togglePermission(permKey)}
                >
                  {permissions[permKey] ? <FaCheck /> : <FaTimes />}
                  {permissions[permKey] ? 'Ativo' : 'Inativo'}
                </ToggleButton>
              </PermissionItem>
            ))}
          </PermissionGrid>
        </PermissionSection>
      ))}

      <SaveButton onClick={handleSave} disabled={loading}>
        <FaSave />
        {loading ? 'Salvando...' : 'Salvar Permissões'}
      </SaveButton>
    </PermissionsContainer>
  );
};

export default PermissionsManager;
