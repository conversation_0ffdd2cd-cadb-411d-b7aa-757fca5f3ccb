import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { usePedido } from '../context/PedidoContext';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Card = styled.div`
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  margin-bottom: 20px;
`;

const Title = styled.h1`
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 700;
`;

const StatusContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
`;

const StatusStep = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 12px;
  background: ${props => props.active ? 
    'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 
    '#f8f9fa'};
  border: 2px solid ${props => props.active ? '#28a745' : '#e0e0e0'};
  transition: all 0.3s ease;
`;

const StatusIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: ${props => props.active ? '#28a745' : '#e0e0e0'};
  color: ${props => props.active ? 'white' : '#666'};
  transition: all 0.3s ease;
`;

const StatusInfo = styled.div`
  flex: 1;
`;

const StatusTitle = styled.h3`
  margin: 0 0 5px 0;
  color: ${props => props.active ? '#155724' : '#666'};
  font-size: 18px;
`;

const StatusDescription = styled.p`
  margin: 0;
  color: ${props => props.active ? '#155724' : '#999'};
  font-size: 14px;
`;

const PedidoInfo = styled.div`
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const InfoLabel = styled.span`
  font-weight: 600;
  color: #2c3e50;
`;

const InfoValue = styled.span`
  color: #666;
`;

const FeedbackContainer = styled.div`
  background: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
`;

const FeedbackTitle = styled.h4`
  margin: 0 0 10px 0;
  color: #856404;
`;

const FeedbackText = styled.p`
  margin: 0;
  color: #856404;
  font-style: italic;
`;

const RefreshButton = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }
`;

const ErrorCard = styled(Card)`
  text-align: center;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 2px solid #dc3545;
`;

const AcompanharPedido = () => {
  const { pedidoId } = useParams();
  const { buscarPedidoPorId, STATUS_PEDIDO } = usePedido();
  const [pedido, setPedido] = useState(null);
  const [loading, setLoading] = useState(true);

  const carregarPedido = () => {
    setLoading(true);
    try {
      const pedidoEncontrado = buscarPedidoPorId(pedidoId);
      setPedido(pedidoEncontrado);
    } catch (error) {
      console.error('Erro ao carregar pedido:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    carregarPedido();
    
    // Atualizar a cada 10 segundos
    const interval = setInterval(carregarPedido, 10000);
    
    return () => clearInterval(interval);
  }, [pedidoId]);

  const getStatusSteps = () => {
    return [
      {
        key: STATUS_PEDIDO.AGUARDANDO,
        icon: '⏳',
        title: 'Pedido Recebido',
        description: 'Seu pedido foi recebido e está aguardando processamento'
      },
      {
        key: STATUS_PEDIDO.EM_ANDAMENTO,
        icon: '👨‍🍳',
        title: 'Em Preparo',
        description: 'Seu pedido está sendo preparado'
      },
      {
        key: STATUS_PEDIDO.PRONTO,
        icon: '✅',
        title: 'Pronto para Retirada',
        description: 'Seu pedido está pronto! Pode vir buscar'
      },
      {
        key: STATUS_PEDIDO.ENTREGUE,
        icon: '🎉',
        title: 'Entregue',
        description: 'Pedido entregue com sucesso'
      }
    ];
  };

  const isStatusActive = (statusStep) => {
    if (!pedido) return false;
    
    const statusOrder = [
      STATUS_PEDIDO.AGUARDANDO,
      STATUS_PEDIDO.EM_ANDAMENTO,
      STATUS_PEDIDO.PRONTO,
      STATUS_PEDIDO.ENTREGUE
    ];
    
    const currentIndex = statusOrder.indexOf(pedido.status);
    const stepIndex = statusOrder.indexOf(statusStep);
    
    return stepIndex <= currentIndex;
  };

  if (loading) {
    return (
      <Container>
        <Card>
          <Title>Carregando...</Title>
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <div style={{ fontSize: '48px', marginBottom: '20px' }}>⏳</div>
            <p>Buscando informações do seu pedido...</p>
          </div>
        </Card>
      </Container>
    );
  }

  if (!pedido) {
    return (
      <Container>
        <ErrorCard>
          <Title>❌ Pedido Não Encontrado</Title>
          <p>O pedido com ID <strong>{pedidoId}</strong> não foi encontrado.</p>
          <p>Verifique se o link está correto ou se o pedido ainda existe.</p>
        </ErrorCard>
      </Container>
    );
  }

  if (pedido.status === STATUS_PEDIDO.CANCELADO) {
    return (
      <Container>
        <ErrorCard>
          <Title>❌ Pedido Cancelado</Title>
          <p>Este pedido foi cancelado.</p>
          {pedido.feedback && (
            <FeedbackContainer>
              <FeedbackTitle>Motivo do Cancelamento:</FeedbackTitle>
              <FeedbackText>{pedido.feedback}</FeedbackText>
            </FeedbackContainer>
          )}
        </ErrorCard>
      </Container>
    );
  }

  return (
    <Container>
      <Card>
        <Title>📱 Acompanhar Pedido</Title>
        
        <PedidoInfo>
          <InfoRow>
            <InfoLabel>ID do Pedido:</InfoLabel>
            <InfoValue>{pedido.id}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Cliente:</InfoLabel>
            <InfoValue>{pedido.nomeCliente}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Data do Pedido:</InfoLabel>
            <InfoValue>{new Date(pedido.dataCriacao).toLocaleString('pt-BR')}</InfoValue>
          </InfoRow>
          <InfoRow>
            <InfoLabel>Última Atualização:</InfoLabel>
            <InfoValue>{new Date(pedido.dataAtualizacao).toLocaleString('pt-BR')}</InfoValue>
          </InfoRow>
        </PedidoInfo>

        <StatusContainer>
          {getStatusSteps().map((step) => (
            <StatusStep key={step.key} active={isStatusActive(step.key)}>
              <StatusIcon active={isStatusActive(step.key)}>
                {step.icon}
              </StatusIcon>
              <StatusInfo>
                <StatusTitle active={isStatusActive(step.key)}>
                  {step.title}
                </StatusTitle>
                <StatusDescription active={isStatusActive(step.key)}>
                  {step.description}
                </StatusDescription>
              </StatusInfo>
            </StatusStep>
          ))}
        </StatusContainer>

        {pedido.feedback && (
          <FeedbackContainer>
            <FeedbackTitle>💬 Mensagem da Empresa:</FeedbackTitle>
            <FeedbackText>{pedido.feedback}</FeedbackText>
          </FeedbackContainer>
        )}

        <RefreshButton onClick={carregarPedido}>
          🔄 Atualizar Status
        </RefreshButton>
      </Card>
    </Container>
  );
};

export default AcompanharPedido;
