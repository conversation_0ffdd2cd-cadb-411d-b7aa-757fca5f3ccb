# Funcionalidade de Auto-Reset de Senhas

## Descrição

A funcionalidade de Auto-Reset foi implementada para melhorar a experiência do usuário no sistema de geração de senhas. Após gerar uma senha, a página automaticamente volta ao estado normal após um tempo configurável, permitindo que a próxima pessoa gere uma nova senha sem interferência.

## Como Funciona

### 1. Geração de Senha
- Quando uma senha é gerada, ela é exibida normalmente na tela
- Um timer visual aparece mostrando o tempo restante para o reset automático
- O QR Code e botão de impressão ficam disponíveis durante todo o período

### 2. Timer Visual
- Exibe uma contagem regressiva em segundos
- Mostra controles para estender o tempo ou cancelar o timer
- Tem design destacado para chamar atenção do usuário

### 3. Controles do Timer
- **Estender Tempo**: Adiciona mais tempo (configurável) ao timer atual
- **Cancelar Timer**: Para o timer e mantém a senha na tela indefinidamente

### 4. Reset Automático
- Quando o timer chega a zero, a senha é automaticamente removida da tela
- A página volta ao estado inicial para geração de nova senha
- O QR Code também é limpo

## Configuração

### Acessando as Configurações
1. Vá para **Configuração do Layout** no menu
2. Procure pela seção **⏰ Auto-Reset de Senhas**

### Opções Disponíveis

#### Ativar Auto-Reset da Página
- **Checkbox**: Liga/desliga a funcionalidade
- **Padrão**: Ativado
- **Descrição**: Quando desativado, as senhas permanecem na tela indefinidamente

#### Tempo para Reset
- **Tipo**: Slider (5-60 segundos)
- **Padrão**: 15 segundos
- **Descrição**: Define quanto tempo a senha fica visível antes do reset automático

## Benefícios

### Para o Usuário
- **Privacidade**: A senha não fica exposta indefinidamente
- **Facilidade**: Não precisa limpar manualmente a tela
- **Flexibilidade**: Pode estender o tempo se precisar de mais tempo

### Para a Organização
- **Eficiência**: Fluxo mais rápido de atendimento
- **Segurança**: Reduz exposição de informações sensíveis
- **Automação**: Menos intervenção manual necessária

## Casos de Uso

### Cenário 1: Atendimento Rápido
- Usuário gera senha
- Imprime ou escaneia QR code rapidamente
- Timer expira e página reseta automaticamente
- Próximo usuário pode gerar nova senha imediatamente

### Cenário 2: Usuário Precisa de Mais Tempo
- Usuário gera senha
- Precisa de mais tempo para imprimir ou escanear
- Clica em "Estender Tempo" para adicionar mais segundos
- Completa a ação e deixa o timer expirar naturalmente

### Cenário 3: Atendimento Personalizado
- Funcionário gera senha para cliente
- Cancela o timer para explicar o processo
- Senha fica na tela até ser manualmente removida

## Implementação Técnica

### Arquivos Modificados
- `src/context/SenhaContext.jsx`: Adicionada função `limparUltimaSenhaGerada`
- `src/pages/GerarSenha.jsx`: Implementado timer e controles visuais
- `src/pages/ConfiguracaoLayout.jsx`: Adicionadas configurações de auto-reset

### Estados Gerenciados
- `tempoRestante`: Contador em segundos
- `timerAtivo`: Se o timer está rodando
- `autoResetSenha`: Se a funcionalidade está ativada
- `tempoAutoReset`: Tempo configurado para reset

### Componentes Visuais
- `TimerContainer`: Container principal do timer
- `TimerText`: Texto explicativo
- `TimerCount`: Contador visual destacado
- `TimerControls`: Botões de controle
- `TimerButton`: Botões estilizados

## Personalização

### Cores e Estilos
Os componentes do timer seguem o design system do aplicativo:
- **Container**: Fundo amarelo claro com borda laranja
- **Contador**: Texto vermelho destacado
- **Botões**: Azul para estender, vermelho para cancelar

### Responsividade
- Design adaptável para diferentes tamanhos de tela
- Botões com tamanho adequado para touch
- Texto legível em dispositivos móveis

## Compatibilidade

### Navegadores
- Chrome/Chromium (recomendado)
- Firefox
- Safari
- Edge

### Dispositivos
- Desktop
- Tablet
- Smartphone
- Kiosks/Totems

## Troubleshooting

### Timer Não Aparece
- Verifique se `autoResetSenha` está ativado nas configurações
- Confirme se uma senha foi gerada com sucesso

### Timer Não Funciona
- Verifique o console do navegador para erros
- Confirme se o JavaScript está habilitado

### Configurações Não Salvam
- Verifique se o localStorage está funcionando
- Confirme se o usuário tem permissões adequadas

## Futuras Melhorias

### Possíveis Adições
- Som de alerta quando timer está próximo do fim
- Animações mais elaboradas
- Configuração de diferentes tempos por tipo de senha
- Integração com sistema de notificações
