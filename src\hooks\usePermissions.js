import { useAuth } from '../context/AuthContext';

/**
 * Hook personalizado para verificar permissões do usuário
 * Permite controlar o acesso a funcionalidades baseado nas permissões
 */
export const usePermissions = () => {
  const { user } = useAuth();

  // Função para verificar se o usuário tem uma permissão específica
  const hasPermission = (permission) => {
    if (!user || !user.permissoes) {
      return false;
    }
    return user.permissoes[permission] === true;
  };

  // Função para verificar múltiplas permissões (AND - todas devem ser verdadeiras)
  const hasAllPermissions = (permissions) => {
    if (!Array.isArray(permissions)) {
      return hasPermission(permissions);
    }
    return permissions.every(permission => hasPermission(permission));
  };

  // Função para verificar se tem pelo menos uma das permissões (OR - pelo menos uma deve ser verdadeira)
  const hasAnyPermission = (permissions) => {
    if (!Array.isArray(permissions)) {
      return hasPermission(permissions);
    }
    return permissions.some(permission => hasPermission(permission));
  };

  // Verificações específicas para funcionalidades principais
  const canGeneratePassword = () => hasPermission('gerarSenha');
  const canCallPassword = () => hasPermission('chamarSenha');
  const canFinalizePassword = () => hasPermission('finalizarSenha');

  // Verificações para acesso às páginas
  const canAccessPanel = () => hasPermission('acessarPainel');
  const canAccessStatistics = () => hasPermission('acessarEstatisticas');
  const canAccessConfiguration = () => hasPermission('acessarConfiguracao');
  const canAccessDesign = () => hasPermission('acessarDesign');

  // Verificações para sistema de pedidos
  const canManageOrders = () => hasPermission('gerenciarPedidos');
  const canCreateOrderQRCode = () => hasPermission('criarQRCodePedidos');

  // Verificações para funcionalidades avançadas
  const canClearData = () => hasPermission('limparDados');
  const canExportData = () => hasPermission('exportarDados');
  const canConfigurePasswordTypes = () => hasPermission('configurarTiposSenha');
  const canConfigureSound = () => hasPermission('configurarSom');
  const canConfigureLayout = () => hasPermission('configurarLayout');

  // Verificações para acesso público
  const canAllowPublicAccess = () => hasPermission('permitirAcessoPublico');

  // Verificações para configurações da empresa
  const canEditCompanyData = () => hasPermission('editarDadosEmpresa');
  const canChangePassword = () => hasPermission('alterarSenha');

  // Função para verificar se é administrador
  const isAdmin = () => {
    return user?.email === '<EMAIL>';
  };

  // Função para obter todas as permissões do usuário
  const getAllPermissions = () => {
    return user?.permissoes || {};
  };

  // Função para verificar se o usuário tem acesso a uma rota específica
  const canAccessRoute = (route) => {
    const routePermissions = {
      '/': ['gerarSenha'],
      '/chamar': ['chamarSenha'],
      '/painel': ['acessarPainel'],
      '/estatisticas': ['acessarEstatisticas'],
      '/configuracao': ['acessarConfiguracao'],
      '/design': ['acessarDesign'],
      '/admin': [] // Apenas admin pode acessar
    };

    // Se é rota de admin, verificar se é admin
    if (route === '/admin') {
      return isAdmin();
    }

    // Verificar permissões específicas da rota
    const requiredPermissions = routePermissions[route];
    if (!requiredPermissions) {
      return true; // Se não há permissões definidas, permitir acesso
    }

    return hasAnyPermission(requiredPermissions);
  };

  // Função para obter lista de rotas permitidas
  const getAllowedRoutes = () => {
    const routes = [
      { path: '/', name: 'Gerar Senha', permission: 'gerarSenha' },
      { path: '/chamar', name: 'Chamar Senha', permission: 'chamarSenha' },
      { path: '/painel', name: 'Painel', permission: 'acessarPainel' },
      { path: '/estatisticas', name: 'Estatísticas', permission: 'acessarEstatisticas' },
      { path: '/configuracao', name: 'Configuração', permission: 'acessarConfiguracao' },
      { path: '/design', name: 'Design', permission: 'acessarDesign' }
    ];

    return routes.filter(route => hasPermission(route.permission));
  };

  return {
    // Verificações básicas
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    
    // Funcionalidades principais
    canGeneratePassword,
    canCallPassword,
    canFinalizePassword,
    
    // Páginas
    canAccessPanel,
    canAccessStatistics,
    canAccessConfiguration,
    canAccessDesign,
    
    // Sistema de pedidos
    canManageOrders,
    canCreateOrderQRCode,
    
    // Funcionalidades avançadas
    canClearData,
    canExportData,
    canConfigurePasswordTypes,
    canConfigureSound,
    canConfigureLayout,
    
    // Acesso público
    canAllowPublicAccess,
    
    // Configurações da empresa
    canEditCompanyData,
    canChangePassword,
    
    // Utilitários
    isAdmin,
    getAllPermissions,
    canAccessRoute,
    getAllowedRoutes,
    
    // Dados do usuário
    user
  };
};
