import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useSenha } from '../context/SenhaContext'
import { useAuth } from '../context/AuthContext'
import { buscarEstatisticas } from '../config/auth'

const Container = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
`

const Title = styled.h2`
  font-size: 28px;
  margin-bottom: 30px;
  color: #2c3e50;
  position: relative;
  padding-bottom: 15px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 3px;
  }
`

const FiltersContainer = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  gap: 20px;
  align-items: center;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
  }
`

const DateInput = styled.input`
  padding: 12px 14px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
    transform: translateY(-2px);
  }

  &:hover {
    border-color: #bbb;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`

const StatCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
  }
`

const StatTitle = styled.h3`
  font-size: 18px;
  color: #7f8c8d;
  margin-bottom: 18px;
  position: relative;
  padding-bottom: 10px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 2px;
  }
`

const StatValue = styled.div`
  font-size: 36px;
  font-weight: bold;
  color: #2c3e50;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`

const StatUnit = styled.span`
  font-size: 16px;
  color: #7f8c8d;
  margin-left: 5px;
`

const Estatisticas = () => {
  const { getSenhasPorStatus } = useSenha()
  const { user } = useAuth()
  const [dataInicio, setDataInicio] = useState('')
  const [dataFim, setDataFim] = useState('')
  const [loading, setLoading] = useState(true) // Iniciar como loading
  const [error, setError] = useState(null)
  const [estatisticas, setEstatisticas] = useState(null) // Iniciar como null para não mostrar zeros

  // Função para resetar estatísticas
  const resetarEstatisticas = () => {
    console.log('[Estatisticas] Resetando estatísticas')
    setEstatisticas({
      totalSenhas: 0,
      totalAtendidas: 0,
      senhasExpiradas: 0,
      tempoMedioEspera: 0,
      tempoMedioAtendimento: 0,
      tempoMaximoEspera: 0,
      tempoMinimoEspera: 0
    })
    setError(null)
  }

  // Carregar filtros salvos
  useEffect(() => {
    if (user?._id) {
      const savedFilters = localStorage.getItem(`estatisticas_filtros_${user._id}`)
      if (savedFilters) {
        try {
          const { inicio, fim } = JSON.parse(savedFilters)
          if (inicio) setDataInicio(inicio)
          if (fim) setDataFim(fim)
        } catch (error) {
          console.error('[Estatisticas] Erro ao carregar filtros salvos:', error)
        }
      }
    }
  }, [user])

  // Salvar filtros quando mudarem
  useEffect(() => {
    if (user?._id) {
      localStorage.setItem(`estatisticas_filtros_${user._id}`, JSON.stringify({
        inicio: dataInicio,
        fim: dataFim
      }))
    }
  }, [dataInicio, dataFim, user])

  const calcularEstatisticas = async () => {
    if (!user) return

    setLoading(true)
    setError(null)

    try {
      console.log('[Estatisticas] Calculando estatísticas para usuário:', user._id)

      // Primeiro tentar buscar do servidor
      if (user._id !== 'guest') {
        try {
          const statsFromServer = await buscarEstatisticas(dataInicio, dataFim)
          if (statsFromServer && typeof statsFromServer === 'object') {
            console.log('[Estatisticas] Estatísticas recebidas do servidor:', statsFromServer)
            setEstatisticas(statsFromServer)
            return
          }
        } catch (error) {
          console.error('[Estatisticas] Erro ao buscar estatísticas do servidor:', error)
          setError('Erro ao buscar dados do servidor. Calculando localmente...')
        }
      }

      // Calcular estatísticas localmente como fallback
      const senhasAtendidas = getSenhasPorStatus('finalizada', user._id)
      const senhasChamadas = getSenhasPorStatus('chamada', user._id)
      const senhasAguardando = getSenhasPorStatus('aguardando', user._id)

      const todasSenhas = [...senhasAtendidas, ...senhasChamadas, ...senhasAguardando]

      const senhasFiltradas = todasSenhas.filter(senha => {
        if (!dataInicio && !dataFim) return true

        const dataSenha = new Date(senha.horarioGeracao)
        const inicio = dataInicio ? new Date(dataInicio) : null
        const fim = dataFim ? new Date(dataFim) : null

        if (inicio && fim) {
          return dataSenha >= inicio && dataSenha <= fim
        } else if (inicio) {
          return dataSenha >= inicio
        } else if (fim) {
          return dataSenha <= fim
        }

        return true
      })

      const temposEspera = senhasAtendidas
        .filter(senha => senha.horarioGeracao && senha.horarioChamada)
        .map(senha => {
          const geracao = new Date(senha.horarioGeracao)
          const chamada = new Date(senha.horarioChamada)

          if (isNaN(geracao.getTime()) || isNaN(chamada.getTime())) {
            return null
          }

          return (chamada - geracao) / 1000 / 60
        })
        .filter(tempo => tempo !== null)

      const temposAtendimento = senhasAtendidas
        .filter(senha => senha.horarioChamada && senha.horarioFinalizacao)
        .map(senha => {
          const chamada = new Date(senha.horarioChamada)
          const finalizacao = new Date(senha.horarioFinalizacao)

          if (isNaN(chamada.getTime()) || isNaN(finalizacao.getTime())) {
            return null
          }

          return (finalizacao - chamada) / 1000 / 60
        })
        .filter(tempo => tempo !== null)

      const mediaEspera = temposEspera.length > 0
        ? temposEspera.reduce((a, b) => a + b, 0) / temposEspera.length
        : 0

      const mediaAtendimento = temposAtendimento.length > 0
        ? temposAtendimento.reduce((a, b) => a + b, 0) / temposAtendimento.length
        : 0

      const statsCalculadas = {
        totalSenhas: senhasFiltradas.length,
        totalAtendidas: senhasAtendidas.length,
        senhasExpiradas: senhasAguardando.filter(senha => {
          const tempoEspera = (new Date() - new Date(senha.horarioGeracao)) / 1000 / 60
          return tempoEspera > 30
        }).length,
        tempoMedioEspera: mediaEspera.toFixed(1),
        tempoMedioAtendimento: mediaAtendimento.toFixed(1),
        tempoMaximoEspera: Math.max(...temposEspera, 0).toFixed(1),
        tempoMinimoEspera: Math.min(...temposEspera, 0).toFixed(1)
      }

      console.log('[Estatisticas] Estatísticas calculadas localmente:', statsCalculadas)
      setEstatisticas(statsCalculadas)

    } catch (error) {
      console.error('[Estatisticas] Erro ao calcular estatísticas:', error)
      setError('Erro ao calcular estatísticas: ' + error.message)
      resetarEstatisticas()
    } finally {
      setLoading(false)
    }
  }

  // Carregar estatísticas quando usuário ou filtros mudarem
  useEffect(() => {
    if (user) {
      calcularEstatisticas()

      // Atualizar a cada 30 segundos
      const interval = setInterval(calcularEstatisticas, 30000)
      return () => clearInterval(interval)
    }
  }, [dataInicio, dataFim, user])

  return (
    <Container>
      <Title>Estatísticas de Atendimento</Title>

      <FiltersContainer>
        <DateInput
          type="date"
          value={dataInicio}
          onChange={(e) => setDataInicio(e.target.value)}
          placeholder="Data Início"
        />
        <DateInput
          type="date"
          value={dataFim}
          onChange={(e) => setDataFim(e.target.value)}
          placeholder="Data Fim"
        />
        {loading && <div style={{ color: '#3498db', fontWeight: 'bold' }}>Carregando...</div>}
      </FiltersContainer>

      {error && (
        <div style={{
          background: '#fee',
          border: '1px solid #fcc',
          borderRadius: '8px',
          padding: '15px',
          margin: '15px 0',
          color: '#c33'
        }}>
          {error}
        </div>
      )}

      {loading ? (
        <div style={{
          textAlign: 'center',
          padding: '50px',
          fontSize: '18px',
          color: '#3498db'
        }}>
          Carregando estatísticas...
        </div>
      ) : estatisticas ? (
        <StatsGrid>
          <StatCard>
            <StatTitle>Total de Senhas Emitidas</StatTitle>
            <StatValue>{estatisticas.totalSenhas}</StatValue>
          </StatCard>

          <StatCard>
            <StatTitle>Atendimentos Concluídos</StatTitle>
            <StatValue>{estatisticas.totalAtendidas}</StatValue>
          </StatCard>

          <StatCard>
            <StatTitle>Senhas Expiradas</StatTitle>
            <StatValue>{estatisticas.senhasExpiradas}</StatValue>
          </StatCard>

          <StatCard>
            <StatTitle>Tempo Médio de Espera</StatTitle>
            <StatValue>
              {estatisticas.tempoMedioEspera}
              <StatUnit>min</StatUnit>
            </StatValue>
          </StatCard>

          <StatCard>
            <StatTitle>Tempo Médio de Atendimento</StatTitle>
            <StatValue>
              {estatisticas.tempoMedioAtendimento}
              <StatUnit>min</StatUnit>
            </StatValue>
          </StatCard>

          <StatCard>
            <StatTitle>Tempo Máximo de Espera</StatTitle>
            <StatValue>
              {estatisticas.tempoMaximoEspera}
              <StatUnit>min</StatUnit>
            </StatValue>
          </StatCard>
        </StatsGrid>
      ) : (
        <div style={{
          textAlign: 'center',
          padding: '50px',
          fontSize: '16px',
          color: '#7f8c8d'
        }}>
          Nenhum dado disponível
        </div>
      )}
    </Container>
  )
}

export default Estatisticas