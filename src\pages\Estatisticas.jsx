import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useSenha } from '../context/SenhaContext'
import { useAuth } from '../context/AuthContext'
import { buscarEstatisticas } from '../config/auth'

const Container = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
`

const Title = styled.h2`
  font-size: 28px;
  margin-bottom: 30px;
  color: #2c3e50;
  position: relative;
  padding-bottom: 15px;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 3px;
  }
`

const FiltersContainer = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  gap: 20px;
  align-items: center;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
  }
`

const DateInput = styled.input`
  padding: 12px 14px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  
  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
    transform: translateY(-2px);
  }
  
  &:hover {
    border-color: #bbb;
  }
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`

const StatCard = styled.div`
  background-color: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
  }
`

const StatTitle = styled.h3`
  font-size: 18px;
  color: #7f8c8d;
  margin-bottom: 18px;
  position: relative;
  padding-bottom: 10px;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 2px;
  }
`

const StatValue = styled.div`
  font-size: 36px;
  font-weight: bold;
  color: #2c3e50;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
`

const StatUnit = styled.span`
  font-size: 16px;
  color: #7f8c8d;
  margin-left: 5px;
`

const Estatisticas = () => {
  const { getSenhasPorStatus } = useSenha()
  const { user } = useAuth()
  const [dataInicio, setDataInicio] = useState('')
  const [dataFim, setDataFim] = useState('')
  const [estatisticas, setEstatisticas] = useState({
    totalSenhas: 0,
    totalAtendidas: 0,
    senhasExpiradas: 0,
    tempoMedioEspera: 0,
    tempoMedioAtendimento: 0,
    tempoMaximoEspera: 0,
    tempoMinimoEspera: 0
  })

  // Função para limpar completamente as estatísticas
  const limparEstatisticas = () => {
    console.log('[Estatisticas] Limpando completamente os dados de estatísticas');
    
    // Resetar os estados
    setEstatisticas({
      totalSenhas: 0,
      totalAtendidas: 0,
      senhasExpiradas: 0,
      tempoMedioEspera: 0,
      tempoMedioAtendimento: 0,
      tempoMaximoEspera: 0,
      tempoMinimoEspera: 0
    });
    
    // Limpar filtros de data
    setDataInicio('');
    setDataFim('');
    
    // Remover do localStorage e sessionStorage
    const userId = user?._id || 'guest';
    const chavesPotenciais = [
      'estatisticas_cache',
      'estatisticas_filtros',
      'dadosEstatisticas',
      'estatisticas_data',
      'stats_cache',
      'estatisticasDia',
      'estatisticasMes',
      'estatisticasTotal',
      'servidor_estatisticas',
      'cache_server',
      `estatisticas_cache_${userId}`,
      `estatisticas_filtros_${userId}`,
      `dadosEstatisticas_${userId}`,
      `stats_cache_${userId}`
    ];
    
    chavesPotenciais.forEach(chave => {
      if (localStorage.getItem(chave)) {
        localStorage.removeItem(chave);
        console.log(`[Estatisticas] Removida chave: ${chave}`);
      }
      if (sessionStorage.getItem(chave)) {
        sessionStorage.removeItem(chave);
        console.log(`[Estatisticas] Removida chave de sessão: ${chave}`);
      }
    });
    
    // Limpeza geral - verificar por qualquer chave relacionada a estatísticas
    for (let i = 0; i < localStorage.length; i++) {
      const chave = localStorage.key(i);
      if (chave && (
          chave.includes('estatistica') || 
          chave.includes('stats') || 
          chave.includes('dados') ||
          chave.includes('cache'))) {
        localStorage.removeItem(chave);
        console.log(`[Estatisticas] Limpeza geral: removida chave ${chave}`);
      }
    }
    
    // Repetir processo para sessionStorage
    for (let i = 0; i < sessionStorage.length; i++) {
      const chave = sessionStorage.key(i);
      if (chave && (
          chave.includes('estatistica') || 
          chave.includes('stats') || 
          chave.includes('dados') ||
          chave.includes('cache'))) {
        sessionStorage.removeItem(chave);
        console.log(`[Estatisticas] Limpeza geral session: removida chave ${chave}`);
      }
    }
    
    // Definir um flag local para evitar recálculos imediatos
    window._estatisticasLimpas = true;
    
    console.log('[Estatisticas] Limpeza de estatísticas concluída com sucesso');
  };

  // Verificar flag de limpeza ao inicializar e após alterações
  useEffect(() => {
    // Função para verificar flags de limpeza
    const verificarFlagsLimpeza = () => {
      // Verificar se está recarregando após limpeza
      const limpezaCompleta = localStorage.getItem('_limpeza_completa');
      if (limpezaCompleta) {
        console.log('[Estatisticas] Detectada reinicialização após limpeza completa');
        localStorage.removeItem('_limpeza_completa');
        limparEstatisticas();
        return true;
      }
      
      // Verificar flag específico de estatísticas limpas
      const estatisticasLimpas = localStorage.getItem('_estatisticas_limpas');
      if (estatisticasLimpas) {
        console.log('[Estatisticas] Detectado flag de estatísticas limpas');
        localStorage.removeItem('_estatisticas_limpas');
        limparEstatisticas();
        return true;
      }
      
      // Verificar flag de limpeza solicitada
      const limpezaSolicitada = localStorage.getItem('_limpeza_solicitada');
      if (limpezaSolicitada) {
        console.log('[Estatisticas] Detectada solicitação de limpeza');
        limparEstatisticas();
        return true;
      }
      
      // Verificar timestamp de limpeza
      const timestampLimpeza = localStorage.getItem('_timestamp_limpeza');
      if (timestampLimpeza) {
        const tempoDecorrido = Date.now() - parseInt(timestampLimpeza);
        if (tempoDecorrido < 30000) { // 30 segundos
          console.log('[Estatisticas] Detectado timestamp recente de limpeza:', tempoDecorrido, 'ms');
          limparEstatisticas();
          return true;
        }
      }
      
      return false;
    };
    
    // Verificar imediatamente
    if (verificarFlagsLimpeza()) {
      console.log('[Estatisticas] Limpeza detectada, bloqueando cálculos');
      return;
    }
    
    // Detectar eventos de storage para limpeza de dados
    const handleStorageChange = (e) => {
      // Verificar pela remoção de chaves (limpeza)
      if (e.key === null || 
          (e.key === '_limpeza_completa' && e.newValue) ||
          (e.key === '_estatisticas_limpas' && e.newValue) ||
          (e.key === '_limpeza_solicitada' && e.newValue) ||
          (e.key === '_timestamp_limpeza' && e.newValue) ||
          (e.key?.includes('estatistica') && !e.newValue) ||
          (e.key?.includes('stats') && !e.newValue) ||
          (e.key?.includes('dados') && !e.newValue) ||
          (e.key?.includes('cache') && !e.newValue)) {
        console.log('[Estatisticas] Detectada limpeza de dados:', e.key);
        limparEstatisticas();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    // Também verificar a cada 5 segundos, pois algumas vezes eventos de storage podem não ser capturados
    const intervalCheck = setInterval(verificarFlagsLimpeza, 5000);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(intervalCheck);
    };
  }, []);  // Este useEffect deve rodar apenas uma vez, a função limparEstatisticas tem acesso ao user atualizado

  useEffect(() => {
    const userId = user?._id || 'guest'
    const savedFilters = localStorage.getItem(`estatisticas_filtros_${userId}`)
    
    if (savedFilters) {
      try {
        const { inicio, fim } = JSON.parse(savedFilters)
        if (inicio) setDataInicio(inicio)
        if (fim) setDataFim(fim)
        console.log(`[Estatisticas] Filtros carregados para usuário: ${userId}`)
      } catch (error) {
        console.error('[Estatisticas] Erro ao carregar filtros salvos:', error)
      }
    }
  }, [user])

  useEffect(() => {
    const userId = user?._id || 'guest'
    localStorage.setItem(`estatisticas_filtros_${userId}`, JSON.stringify({
      inicio: dataInicio,
      fim: dataFim
    }))
  }, [dataInicio, dataFim, user])

  const calcularEstatisticas = async () => {
    try {
      const userId = user?._id || 'guest'
      console.log('[Estatisticas] Calculando estatísticas para usuário:', userId)
      
      // Verificar bloqueio temporário após limpeza
      if (window._estatisticasLimpas) {
        console.log('[Estatisticas] Cálculo bloqueado por limpeza recente');
        setTimeout(() => { window._estatisticasLimpas = false; }, 5000);
        return;
      }
      
      // Verificar se está em processo de limpeza - bloquear cálculos
      if (localStorage.getItem('_limpeza_solicitada') || 
          localStorage.getItem('_limpeza_completa') ||
          localStorage.getItem('_estatisticas_limpas') ||
          localStorage.getItem('_timestamp_limpeza')) {
        console.log('[Estatisticas] Cálculo ignorado - sistema em processo de limpeza');
        limparEstatisticas();
        return;
      }
      
      if (userId !== 'guest') {
        try {
          const statsFromServer = await buscarEstatisticas(dataInicio, dataFim)
          if (statsFromServer && typeof statsFromServer === 'object') {
            // Verificar se os dados do servidor estão vazios ou nulos
            const todasZero = !statsFromServer.totalSenhas && 
                              !statsFromServer.totalAtendidas && 
                              !statsFromServer.senhasExpiradas;
                              
            if (todasZero) {
              console.log('[Estatisticas] Dados do servidor vazios, mantendo estado zerado');
              limparEstatisticas();
              return;
            }
            
            console.log('[Estatisticas] Estatísticas recebidas do servidor:', statsFromServer)
            setEstatisticas(statsFromServer)
            localStorage.setItem(`estatisticas_cache_${userId}`, JSON.stringify(statsFromServer))
            return
          }
        } catch (error) {
          console.error('[Estatisticas] Erro ao buscar estatísticas do servidor:', error)
        }
      }
      
      const senhasAtendidas = getSenhasPorStatus('finalizada', userId)
      const senhasChamadas = getSenhasPorStatus('chamada', userId)
      const senhasAguardando = getSenhasPorStatus('aguardando', userId)
      
      if (senhasAtendidas.length === 0 && senhasChamadas.length === 0 && senhasAguardando.length === 0) {
        console.log('[Estatisticas] Nenhuma senha encontrada, resetando estatísticas');
        limparEstatisticas();
        return;
      }
      
      const todasSenhas = [...senhasAtendidas, ...senhasChamadas, ...senhasAguardando]
      
      const senhasFiltradas = todasSenhas.filter(senha => {
        if (!dataInicio && !dataFim) return true
        
        const dataSenha = new Date(senha.horarioGeracao)
        const inicio = dataInicio ? new Date(dataInicio) : null
        const fim = dataFim ? new Date(dataFim) : null
        
        if (inicio && fim) {
          return dataSenha >= inicio && dataSenha <= fim
        } else if (inicio) {
          return dataSenha >= inicio
        } else if (fim) {
          return dataSenha <= fim
        }
        
        return true
      })
      
      const temposEspera = senhasAtendidas
        .filter(senha => senha.horarioGeracao && senha.horarioChamada)
        .map(senha => {
          const geracao = new Date(senha.horarioGeracao)
          const chamada = new Date(senha.horarioChamada)
          
          if (isNaN(geracao.getTime()) || isNaN(chamada.getTime())) {
            return null
          }
          
          return (chamada - geracao) / 1000 / 60
        })
        .filter(tempo => tempo !== null)
      
      const temposAtendimento = senhasAtendidas
        .filter(senha => senha.horarioChamada && senha.horarioFinalizacao)
        .map(senha => {
          const chamada = new Date(senha.horarioChamada)
          const finalizacao = new Date(senha.horarioFinalizacao)
          
          if (isNaN(chamada.getTime()) || isNaN(finalizacao.getTime())) {
            return null
          }
          
          return (finalizacao - chamada) / 1000 / 60
        })
        .filter(tempo => tempo !== null)
      
      const mediaEspera = temposEspera.length > 0
        ? temposEspera.reduce((a, b) => a + b, 0) / temposEspera.length
        : 0
      
      const mediaAtendimento = temposAtendimento.length > 0
        ? temposAtendimento.reduce((a, b) => a + b, 0) / temposAtendimento.length
        : 0
      
      const statsCalculadas = {
        totalSenhas: senhasFiltradas.length,
        totalAtendidas: senhasAtendidas.length,
        senhasExpiradas: senhasAguardando.filter(senha => {
          const tempoEspera = (new Date() - new Date(senha.horarioGeracao)) / 1000 / 60
          return tempoEspera > 30
        }).length,
        tempoMedioEspera: mediaEspera.toFixed(1),
        tempoMedioAtendimento: mediaAtendimento.toFixed(1),
        tempoMaximoEspera: Math.max(...temposEspera, 0).toFixed(1),
        tempoMinimoEspera: Math.min(...temposEspera, 0).toFixed(1)
      }
      
      // Verificar se todos os valores são zero - caso sejam, pode ser um erro ou o sistema está limpo
      const todasZero = !statsCalculadas.totalSenhas && 
                        !statsCalculadas.totalAtendidas && 
                        !statsCalculadas.senhasExpiradas;
                      
      if (todasZero && (
          localStorage.getItem('_limpeza_solicitada') || 
          localStorage.getItem('_timestamp_limpeza'))) {
        console.log('[Estatisticas] Detectado resultado vazio após solicitação de limpeza');
        limparEstatisticas();
        return;
      }
      
      setEstatisticas(statsCalculadas)
      
      localStorage.setItem(`estatisticas_cache_${userId}`, JSON.stringify(statsCalculadas))
    } catch (error) {
      console.error('[Estatisticas] Erro ao calcular estatísticas:', error)
      // Em caso de erro, limpar estatísticas para garantir estado consistente
      limparEstatisticas();
    }
  }

  useEffect(() => {
    // Verificar se há processo de limpeza em andamento
    if (localStorage.getItem('_limpeza_solicitada') || 
        localStorage.getItem('_limpeza_completa') ||
        localStorage.getItem('_estatisticas_limpas') ||
        localStorage.getItem('_timestamp_limpeza') ||
        window._estatisticasLimpas) {
      console.log('[Estatisticas] Carregamento bloqueado - sistema em processo de limpeza');
      limparEstatisticas();
      return;
    }
    
    // Atualiza quando os filtros de data ou usuário mudam
    calcularEstatisticas();
    
    // Configura intervalo para atualização periódica
    const interval = setInterval(calcularEstatisticas, 30000);
    return () => clearInterval(interval);
  }, [dataInicio, dataFim, user]);

  return (
    <Container>
      <Title>Estatísticas de Atendimento</Title>
      
      <FiltersContainer>
        <DateInput
          type="date"
          value={dataInicio}
          onChange={(e) => setDataInicio(e.target.value)}
          placeholder="Data Início"
        />
        <DateInput
          type="date"
          value={dataFim}
          onChange={(e) => setDataFim(e.target.value)}
          placeholder="Data Fim"
        />
      </FiltersContainer>
      
      <StatsGrid>
        <StatCard>
          <StatTitle>Total de Senhas Emitidas</StatTitle>
          <StatValue>{estatisticas.totalSenhas}</StatValue>
        </StatCard>
        
        <StatCard>
          <StatTitle>Atendimentos Concluídos</StatTitle>
          <StatValue>{estatisticas.totalAtendidas}</StatValue>
        </StatCard>
        
        <StatCard>
          <StatTitle>Senhas Expiradas</StatTitle>
          <StatValue>{estatisticas.senhasExpiradas}</StatValue>
        </StatCard>
        
        <StatCard>
          <StatTitle>Tempo Médio de Espera</StatTitle>
          <StatValue>
            {estatisticas.tempoMedioEspera}
            <StatUnit>min</StatUnit>
          </StatValue>
        </StatCard>
        
        <StatCard>
          <StatTitle>Tempo Médio de Atendimento</StatTitle>
          <StatValue>
            {estatisticas.tempoMedioAtendimento}
            <StatUnit>min</StatUnit>
          </StatValue>
        </StatCard>
        
        <StatCard>
          <StatTitle>Tempo Máximo de Espera</StatTitle>
          <StatValue>
            {estatisticas.tempoMaximoEspera}
            <StatUnit>min</StatUnit>
          </StatValue>
        </StatCard>
      </StatsGrid>
    </Container>
  )
}

export default Estatisticas