import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import axios from 'axios'
import { useAuth } from '../context/AuthContext'
import layoutThemes from '../themes/layoutThemes'
import { 
  updateUserConfig, 
  getCurrentUser,
  limparDadosNoServidorDireto
} from '../config/auth'

const Container = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
`

const Title = styled.h2`
  font-size: 28px;
  margin-bottom: 30px;
  color: #2c3e50;
`

const Section = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
`

const SectionTitle = styled.h3`
  font-size: 20px;
  margin-bottom: 20px;
  color: #2c3e50;
`

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`

const ThemeCard = styled.div`
  border: 2px solid ${props => props.selected ? '#3498db' : '#ddd'};
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`

const ColorInput = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  
  label {
    min-width: 120px;
  }
  
  input[type="color"] {
    width: 50px;
    height: 30px;
    padding: 0;
    border: none;
    border-radius: 4px;
  }
`

const FontSelect = styled.select`
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  margin-bottom: 15px;
`

const RangeInput = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  
  label {
    min-width: 120px;
  }
  
  input[type="range"] {
    flex: 1;
  }
  
  span {
    min-width: 50px;
    text-align: right;
  }
`

const FileInput = styled.div`
  margin-bottom: 15px;
  
  input[type="file"] {
    display: none;
  }
  
  label {
    display: inline-block;
    padding: 8px 16px;
    background-color: #3498db;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #2980b9;
    }
  }
`

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
`

const Select = styled.select`
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 16px;
  margin-bottom: 15px;
`

const Button = styled.button`
  background-color: #2ecc71;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #27ae60;
  }
`

const DangerButton = styled(Button)`
  background-color: #e74c3c;
  
  &:hover {
    background-color: #c0392b;
  }
`

const OpenPanelButton = styled(Button)`
  background-color: #3498db;
  margin-right: 10px;
  
  &:hover {
    background-color: #2980b9;
  }
`

const CopyLinkButton = styled(Button)`
  background-color: #9b59b6;
  
  &:hover {
    background-color: #8e44ad;
  }
`

const Preview = styled.div`
  background-color: ${props => props.backgroundColor};
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: ${props => props.textColor};
  font-family: ${props => props.fontFamily};
  font-size: ${props => props.fontSize}px;
  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
`

const ConfiguracaoLayout = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState({
    theme: 'light',
    layoutTheme: 'padrao',
    backgroundColor: '#f8f9fa',
    textColor: '#2c3e50',
    senhaColor: '#3498db',
    fontFamily: 'Arial',
    fontSize: 48,
    logo: null,
    backgroundType: 'color',
    backgroundImage: null,
    footerText: '',
    // Configurações de áudio
    voiceType: 'default',
    volume: 80,
    soundEffect: 'bell',
    repeatInterval: 1,
    // Configurações de impressão
    impressaoAutomatica: false,
    tipoImpressora: 'termica',
    larguraImpressao: 80,
    alturaImpressao: 'auto',
    // Configuração de tipos de senha personalizados
    tiposSenha: {
      P: {
        nome: 'Prioritária',
        descricao: 'Senha Prioritária',
        cor: '#e74c3c',
        ativo: true
      },
      N: {
        nome: 'Normal',
        descricao: 'Senha Normal',
        cor: '#3498db',
        ativo: true
      },
      R: {
        nome: 'Rápido',
        descricao: 'Atendimento Rápido',
        cor: '#2ecc71',
        ativo: true
      }
    }
  })
  
  // Importa os temas de layout definidos
  const { useEffect: useLayoutEffect } = React;
  // O layoutThemes já está sendo importado no topo do arquivo, não precisamos importá-lo novamente
  
  // Temas de cores
  const themes = [
    { id: 'light', name: 'Claro', colors: { bg: '#f8f9fa', text: '#2c3e50', senha: '#3498db' } },
    { id: 'dark', name: 'Escuro', colors: { bg: '#2c3e50', text: '#ecf0f1', senha: '#3498db' } },
    { id: 'minimal', name: 'Minimalista', colors: { bg: '#ffffff', text: '#000000', senha: '#666666' } },
    { id: 'colorful', name: 'Colorido', colors: { bg: '#e8f4f8', text: '#2c3e50', senha: '#e74c3c' } }
  ]
  
  const fonts = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Georgia',
    'Verdana',
    'Roboto',
    'Open Sans'
  ]
  
  useEffect(() => {
    // Carrega configurações salvas com prefixo do userId
    const userId = user?._id || 'guest';
    const savedConfig = localStorage.getItem(`painelConfig_${userId}`);
    
    if (savedConfig) {
      try {
        setConfig(JSON.parse(savedConfig));
        console.log(`Configurações carregadas para usuário: ${userId}`);
      } catch (error) {
        console.error('Erro ao carregar configurações:', error);
        // Em caso de erro, mantém as configurações padrão
      }
    } else {
      console.log(`Nenhuma configuração encontrada para usuário: ${userId}, usando padrões`);
    }
  }, [user]);
  
  const handleThemeChange = (theme) => {
    setConfig({
      ...config,
      theme: theme.id,
      backgroundColor: theme.colors.bg,
      textColor: theme.colors.text,
      senhaColor: theme.colors.senha
      // Mantém o layoutTheme atual
    })
  }
  
  const handleLayoutChange = (layoutId) => {
    setConfig({
      ...config,
      layoutTheme: layoutId
      // Mantém as cores atuais
    })
  }
  
  const handleLogoUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setConfig({ ...config, logo: reader.result })
      }
      reader.readAsDataURL(file)
    }
  }
  
  const handleBackgroundUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        setConfig({
          ...config,
          backgroundType: 'image',
          backgroundImage: reader.result
        })
      }
      reader.readAsDataURL(file)
    }
  }
  
  const saveConfig = () => {
    // Salvar configurações com prefixo do userId
    const userId = user?._id || 'guest';
    localStorage.setItem(`painelConfig_${userId}`, JSON.stringify(config));
    console.log(`Configurações salvas para usuário: ${userId}`);
    alert('Configurações salvas com sucesso!');
  }
  
  const handleOpenPanel = () => {
    const url = `${window.location.origin}/painel-publico`
    window.open(url, '_blank', 'fullscreen=yes')
  }

  const handleCopyLink = () => {
    const url = `${window.location.origin}/painel-publico`
    navigator.clipboard.writeText(url)
    alert('Link do painel copiado para a área de transferência!')
  }
  
  const limparDadosServidor = async () => {
    // Confirmar antes de limpar
    const confirmar = window.confirm(
      'ATENÇÃO: Esta ação tentará limpar os dados DIRETAMENTE NO SERVIDOR, contornando o proxy do Vercel. Esta ação não pode ser desfeita. Deseja continuar?'
    )
    
    if (!confirmar) return
    
    try {
      setLoading(true)
      
      // Chamar diretamente a função que acessa o servidor Render
      const resultadoServidor = await limparDadosNoServidorDireto()
      console.log('Resultado da limpeza direta no servidor:', resultadoServidor)
      
      if (resultadoServidor.success) {
        alert(`Sucesso! Dados limpos diretamente no servidor. ${resultadoServidor.senhasRemovidas || 0} senhas foram removidas.`)
      } else {
        alert(`Erro ao limpar dados no servidor: ${resultadoServidor.error || 'Erro desconhecido'}\n\nTente novamente ou entre em contato com o suporte.`)
      }
    } catch (error) {
      console.error('Erro ao tentar limpar dados no servidor:', error)
      alert('Ocorreu um erro ao tentar limpar os dados no servidor: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container>
      <Title>Configuração do Layout</Title>
      
      <Section>
        <SectionTitle>Painel Público</SectionTitle>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
          <OpenPanelButton onClick={handleOpenPanel}>
            Abrir Painel em Tela Cheia
          </OpenPanelButton>
          <CopyLinkButton onClick={handleCopyLink}>
            Copiar Link do Painel
          </CopyLinkButton>
        </div>
      </Section>

      <Section>
        <SectionTitle>Gerador de Senhas</SectionTitle>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
          <OpenPanelButton onClick={() => window.open(`${window.location.origin}/gerar-senha-publico`, '_blank', 'fullscreen=yes')}>
            Abrir Gerador em Tela Cheia
          </OpenPanelButton>
          <CopyLinkButton onClick={() => {
            const url = `${window.location.origin}/gerar-senha`
            navigator.clipboard.writeText(url)
            alert('Link do gerador copiado para a área de transferência!')
          }}>
            Copiar Link do Gerador
          </CopyLinkButton>
        </div>
      </Section>
      
      <Section>
        <SectionTitle>Temas Predefinidos</SectionTitle>
        <Grid>
          {themes.map(theme => (
            <ThemeCard
              key={theme.id}
              selected={config.theme === theme.id}
              onClick={() => handleThemeChange(theme)}
              style={{
                backgroundColor: theme.colors.bg,
                color: theme.colors.text
              }}
            >
              {theme.name}
            </ThemeCard>
          ))}
        </Grid>
      </Section>
      
      <Section>
        <SectionTitle>Layouts do Painel</SectionTitle>
        <p style={{ marginBottom: '15px' }}>Escolha o layout que melhor se adapta às suas necessidades:</p>
        <Grid>
          {layoutThemes && Object.values(layoutThemes).map(layout => (
            <ThemeCard
              key={layout.id}
              selected={config.layoutTheme === layout.id}
              onClick={() => handleLayoutChange(layout.id)}
              style={{
                backgroundColor: layout.colors.bg,
                color: layout.colors.text,
                border: config.layoutTheme === layout.id ? `2px solid ${layout.colors.senha}` : '2px solid #ddd'
              }}
            >
              <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>{layout.name}</div>
              <div style={{ fontSize: '12px' }}>{layout.description}</div>
            </ThemeCard>
          ))}
        </Grid>
      </Section>

      <Section>
        <SectionTitle>Personalização Avançada</SectionTitle>
        <p style={{ marginBottom: '15px' }}>Personalize ainda mais o seu painel de senhas:</p>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
          <Label>Texto de Boas-vindas:</Label>
          <input
            type="text"
            placeholder="Bem-vindo ao nosso sistema de atendimento"
            value={config.welcomeText || ''}
            onChange={(e) => setConfig({ ...config, welcomeText: e.target.value })}
            style={{ width: '100%', padding: '8px' }}
          />
          
          <Label>Mostrar relógio no painel:</Label>
          <label style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <input
              type="checkbox"
              checked={config.showClock !== false}
              onChange={(e) => setConfig({ ...config, showClock: e.target.checked })}
            />
            Exibir relógio no painel
          </label>
          
          <Label>Formato de exibição da senha:</Label>
          <Select
            value={config.senhaFormat || 'default'}
            onChange={(e) => setConfig({ ...config, senhaFormat: e.target.value })}
          >
            <option value="default">Padrão (A123)</option>
            <option value="simple">Simples (123)</option>
            <option value="full">Completo (SENHA A123)</option>
          </Select>
          
          <Label>Animação de chamada:</Label>
          <Select
            value={config.callAnimation || 'fade'}
            onChange={(e) => setConfig({ ...config, callAnimation: e.target.value })}
          >
            <option value="fade">Fade</option>
            <option value="slide">Slide</option>
            <option value="bounce">Bounce</option>
            <option value="none">Sem animação</option>
          </Select>
        </div>
      </Section>
      
      <Section>
        <SectionTitle>Cores Personalizadas</SectionTitle>
        <ColorInput>
          <label>Cor de Fundo:</label>
          <input
            type="color"
            value={config.backgroundColor}
            onChange={(e) => setConfig({ ...config, backgroundColor: e.target.value })}
          />
        </ColorInput>
        <ColorInput>
          <label>Cor do Texto:</label>
          <input
            type="color"
            value={config.textColor}
            onChange={(e) => setConfig({ ...config, textColor: e.target.value })}
          />
        </ColorInput>
        <ColorInput>
          <label>Cor da Senha:</label>
          <input
            type="color"
            value={config.senhaColor}
            onChange={(e) => setConfig({ ...config, senhaColor: e.target.value })}
          />
        </ColorInput>
      </Section>
      
      <Section>
        <SectionTitle>Fonte e Tamanho</SectionTitle>
        <FontSelect
          value={config.fontFamily}
          onChange={(e) => setConfig({ ...config, fontFamily: e.target.value })}
        >
          {fonts.map(font => (
            <option key={font} value={font}>{font}</option>
          ))}
        </FontSelect>
        
        <RangeInput>
          <label>Tamanho da Fonte:</label>
          <input
            type="range"
            min="24"
            max="72"
            value={config.fontSize}
            onChange={(e) => setConfig({ ...config, fontSize: parseInt(e.target.value) })}
          />
          <span>{config.fontSize}px</span>
        </RangeInput>
      </Section>
      
      <Section>
        <SectionTitle>Logo e Identidade Visual</SectionTitle>
        <FileInput>
          <label htmlFor="logo-upload">Upload Logo</label>
          <input
            id="logo-upload"
            type="file"
            accept="image/*"
            onChange={handleLogoUpload}
          />
        </FileInput>
        
        <input
          type="text"
          placeholder="Texto do Rodapé"
          value={config.footerText}
          onChange={(e) => setConfig({ ...config, footerText: e.target.value })}
          style={{ width: '100%', padding: '8px', marginBottom: '15px' }}
        />
      </Section>
      
      <Section>
        <SectionTitle>Plano de Fundo</SectionTitle>
        <div style={{ marginBottom: '15px' }}>
          <label style={{ marginRight: '15px' }}>
            <input
              type="radio"
              name="backgroundType"
              value="color"
              checked={config.backgroundType === 'color'}
              onChange={(e) => setConfig({ ...config, backgroundType: e.target.value })}
            /> Cor Sólida
          </label>
          <label>
            <input
              type="radio"
              name="backgroundType"
              value="image"
              checked={config.backgroundType === 'image'}
              onChange={(e) => setConfig({ ...config, backgroundType: e.target.value })}
            /> Imagem
          </label>
        </div>
        
        {config.backgroundType === 'image' && (
          <FileInput>
            <label htmlFor="background-upload">Upload Imagem de Fundo</label>
            <input
              id="background-upload"
              type="file"
              accept="image/*"
              onChange={handleBackgroundUpload}
            />
          </FileInput>
        )}
      </Section>
      
      <Section>
        <SectionTitle>🔊 Configuração de Áudio</SectionTitle>
        
        <RangeInput>
          <label>Volume:</label>
          <input
            type="range"
            min="0"
            max="100"
            value={config.volume}
            onChange={(e) => setConfig({ ...config, volume: parseInt(e.target.value) })}
          />
          <span>{config.volume}%</span>
        </RangeInput>
        
        <Label>Efeito Sonoro:</Label>
        <Select
          value={config.soundEffect}
          onChange={(e) => setConfig({ ...config, soundEffect: e.target.value })}
        >
          <option value="bell">Campainha</option>
          <option value="beep">Bipe</option>
          <option value="chime">Sino</option>
          <option value="none">Sem som</option>
        </Select>
        
        <Label>Tipo de Voz:</Label>
        <Select
          value={config.voiceType}
          onChange={(e) => setConfig({ ...config, voiceType: e.target.value })}
        >
          <option value="default">Padrão</option>
          <option value="male">Masculina</option>
          <option value="female">Feminina</option>
          <option value="none">Sem voz</option>
        </Select>
        
        <Label>Repetir Chamada:</Label>
        <Select
          value={config.repeatInterval}
          onChange={(e) => setConfig({ ...config, repeatInterval: parseInt(e.target.value) })}
        >
          <option value="1">1 vez</option>
          <option value="2">2 vezes</option>
          <option value="3">3 vezes</option>
          <option value="0">Não repetir</option>
        </Select>
      </Section>
      
      <Section>
        <SectionTitle>🖨️ Configuração de Impressão</SectionTitle>
        
        <div style={{ marginBottom: '15px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
            <input
              type="checkbox"
              checked={config.impressaoAutomatica}
              onChange={(e) => setConfig({ ...config, impressaoAutomatica: e.target.checked })}
            />
            Impressão Automática de Senhas
          </label>
        </div>
        
        <Label>Tipo de Impressora:</Label>
        <Select
          value={config.tipoImpressora}
          onChange={(e) => setConfig({ ...config, tipoImpressora: e.target.value })}
        >
          <option value="termica">Impressora Térmica</option>
          <option value="padrao">Impressora Padrão</option>
          <option value="escpos">ESC/POS (Impressora de Cupom)</option>
        </Select>
        
        <RangeInput>
          <label>Largura do Papel:</label>
          <input
            type="range"
            min="58"
            max="100"
            value={config.larguraImpressao}
            onChange={(e) => setConfig({ ...config, larguraImpressao: parseInt(e.target.value) })}
          />
          <span>{config.larguraImpressao}mm</span>
        </RangeInput>
      </Section>
      
      <Section>
        <SectionTitle>🎫 Configuração de Tipos de Senha</SectionTitle>
        <p style={{ marginBottom: '15px' }}>Personalize os tipos de senha disponíveis no sistema:</p>
        
        {Object.entries(config.tiposSenha || {}).map(([tipo, tipoConfig]) => (
          <div key={tipo} style={{ 
            border: '1px solid #ddd', 
            borderRadius: '8px', 
            padding: '15px', 
            marginBottom: '15px',
            backgroundColor: tipoConfig.ativo !== false ? '#fff' : '#f5f5f5'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h4 style={{ margin: '0', fontSize: '18px' }}>
                Tipo: {tipo} - {tipoConfig.nome || 'Sem nome'}
              </h4>
              <label style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                <input
                  type="checkbox"
                  checked={tipoConfig.ativo !== false}
                  onChange={(e) => {
                    const updatedTiposSenha = { ...config.tiposSenha };
                    updatedTiposSenha[tipo] = { 
                      ...updatedTiposSenha[tipo],
                      ativo: e.target.checked 
                    };
                    setConfig({ ...config, tiposSenha: updatedTiposSenha });
                  }}
                />
                Ativo
              </label>
            </div>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
              <div>
                <Label>Nome do Botão:</Label>
                <input
                  type="text"
                  value={tipoConfig.nome || ''}
                  placeholder="Nome exibido no botão"
                  onChange={(e) => {
                    const updatedTiposSenha = { ...config.tiposSenha };
                    updatedTiposSenha[tipo] = { 
                      ...updatedTiposSenha[tipo],
                      nome: e.target.value 
                    };
                    setConfig({ ...config, tiposSenha: updatedTiposSenha });
                  }}
                  style={{ width: '100%', padding: '8px', marginBottom: '10px' }}
                />
                
                <Label>Descrição:</Label>
                <input
                  type="text"
                  value={tipoConfig.descricao || ''}
                  placeholder="Descrição da senha"
                  onChange={(e) => {
                    const updatedTiposSenha = { ...config.tiposSenha };
                    updatedTiposSenha[tipo] = { 
                      ...updatedTiposSenha[tipo],
                      descricao: e.target.value 
                    };
                    setConfig({ ...config, tiposSenha: updatedTiposSenha });
                  }}
                  style={{ width: '100%', padding: '8px', marginBottom: '10px' }}
                />
              </div>
              
              <div>
                <Label>Emoji/Ícone:</Label>
                <input
                  type="text"
                  value={tipoConfig.emoji || ''}
                  placeholder="Emoji (ex: 🔴, 🔵, 🟢)"
                  onChange={(e) => {
                    const updatedTiposSenha = { ...config.tiposSenha };
                    updatedTiposSenha[tipo] = { 
                      ...updatedTiposSenha[tipo],
                      emoji: e.target.value 
                    };
                    setConfig({ ...config, tiposSenha: updatedTiposSenha });
                  }}
                  style={{ width: '100%', padding: '8px', marginBottom: '10px' }}
                />
                
                <Label>Cor:</Label>
                <ColorInput>
                  <input
                    type="color"
                    value={tipoConfig.cor || '#7f8c8d'}
                    onChange={(e) => {
                      const updatedTiposSenha = { ...config.tiposSenha };
                      updatedTiposSenha[tipo] = { 
                        ...updatedTiposSenha[tipo],
                        cor: e.target.value 
                      };
                      setConfig({ ...config, tiposSenha: updatedTiposSenha });
                    }}
                  />
                  <span style={{ marginLeft: '10px' }}>{tipoConfig.cor || '#7f8c8d'}</span>
                </ColorInput>
              </div>
            </div>
            
            <div style={{ 
              backgroundColor: tipoConfig.cor || '#7f8c8d', 
              color: 'white', 
              padding: '10px', 
              borderRadius: '8px',
              marginTop: '10px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '10px'
            }}>
              <span style={{ fontSize: '24px' }}>{tipoConfig.emoji || '🎫'}</span>
              <span>{tipoConfig.nome || 'Senha'}</span>
            </div>
          </div>
        ))}
        
        <Button 
          onClick={() => {
            // Adicionar um novo tipo de senha personalizado
            const tipoId = prompt('Digite o identificador do novo tipo de senha (uma letra única, ex: E):');
            if (tipoId && tipoId.length === 1) {
              const updatedTiposSenha = { ...config.tiposSenha };
              // Verificar se o tipo já existe
              if (updatedTiposSenha[tipoId]) {
                alert('Este tipo de senha já existe!');
                return;
              }
              // Adicionar novo tipo
              updatedTiposSenha[tipoId] = {
                nome: 'Nova Senha',
                descricao: 'Nova Senha',
                cor: '#7f8c8d',
                emoji: '🎫',
                ativo: true
              };
              setConfig({ ...config, tiposSenha: updatedTiposSenha });
            } else if (tipoId) {
              alert('O identificador deve ser uma única letra!');
            }
          }}
          style={{ marginTop: '10px' }}
        >
          Adicionar Novo Tipo de Senha
        </Button>
      </Section>
      
      <Section>
        <SectionTitle>Visualização</SectionTitle>
        <Preview
          backgroundColor={config.backgroundType === 'color' ? config.backgroundColor : 'transparent'}
          textColor={config.textColor}
          fontFamily={config.fontFamily}
          fontSize={config.fontSize}
          backgroundImage={config.backgroundType === 'image' ? config.backgroundImage : null}
        >
          {config.logo && (
            <img 
              src={config.logo} 
              alt="Logo" 
              style={{ maxWidth: '200px', maxHeight: '100px', marginBottom: '20px' }} 
            />
          )}
          <div style={{ color: config.senhaColor, fontWeight: 'bold', fontSize: `${config.fontSize}px` }}>
            A123
          </div>
          <div style={{ marginTop: '10px' }}>Guichê 1</div>
          {config.footerText && (
            <div style={{ marginTop: '30px', fontSize: '14px' }}>
              {config.footerText}
            </div>
          )}
        </Preview>
        
        <Button onClick={saveConfig}>Salvar Configurações</Button>
      </Section>
      
      {/* Seção de Personalização de Tipos de Senha removida para evitar duplicação */}
      
      <Section>
        <SectionTitle>⚠️ Redefinição de Dados</SectionTitle>
        <p style={{ marginBottom: '20px', color: '#555' }}>
          Esta seção permite limpar dados do sistema como senhas, configurações e layouts. Utilize esta opção com cautela, pois a ação não pode ser desfeita.
        </p>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
          <DangerButton 
            onClick={async () => {
              if (window.confirm("Tem certeza que deseja limpar dados no servidor? Esta ação não pode ser desfeita.")) {
                setLoading(true);
                try {
                  const resultado = await limparDadosNoServidorDireto();
                  console.log('Resultado da limpeza:', resultado);
                  alert(`Limpeza: ${resultado.success ? 'Sucesso' : 'Falha'} - ${resultado.message}`);
                } catch (error) {
                  console.error('Erro ao limpar dados:', error);
                  alert(`Erro: ${error.message}`);
                } finally {
                  setLoading(false);
                }
              }
            }}
            disabled={loading}
            style={{ backgroundColor: '#c0392b' }}
          >
            {loading ? 'Processando...' : 'Limpar Dados do Servidor'}
          </DangerButton>
        </div>
        
        <p style={{ fontSize: '14px', color: '#7f8c8d', marginTop: '20px' }}>
          Estas ações irão remover todas as senhas e dados de atendimento da empresa. Use apenas quando necessário para redefinir o sistema.
        </p>
      </Section>
    </Container>
  )
}

export default ConfiguracaoLayout