import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { usePedido } from '../context/PedidoContext';
import { QRCodeSVG } from 'qrcode.react';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Card = styled.div`
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  margin-bottom: 20px;
`;

const Title = styled.h1`
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 700;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
`;

const Input = styled.input`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const TextArea = styled.textarea`
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const Button = styled.button`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const SuccessCard = styled(Card)`
  text-align: center;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 2px solid #28a745;
`;

const SuccessTitle = styled.h2`
  color: #155724;
  margin-bottom: 20px;
  font-size: 24px;
`;

const PedidoInfo = styled.div`
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const QRContainer = styled.div`
  display: flex;
  justify-content: center;
  margin: 20px 0;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const FazerPedido = () => {
  const { empresaId } = useParams();
  const navigate = useNavigate();
  const { criarPedido, gerarUrlAcompanhamento, loading } = usePedido();
  
  const [pedidoCriado, setPedidoCriado] = useState(null);
  const [formData, setFormData] = useState({
    nomeCliente: '',
    telefone: '',
    email: '',
    itens: '',
    observacoes: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const novoPedido = await criarPedido({
        ...formData,
        empresaId
      });
      
      setPedidoCriado(novoPedido);
    } catch (error) {
      console.error('Erro ao criar pedido:', error);
      alert('Erro ao criar pedido. Tente novamente.');
    }
  };

  const isFormValid = formData.nomeCliente && formData.telefone && formData.itens;

  if (pedidoCriado) {
    return (
      <Container>
        <SuccessCard>
          <SuccessTitle>✅ Pedido Criado com Sucesso!</SuccessTitle>
          
          <PedidoInfo>
            <h3>Detalhes do Pedido</h3>
            <p><strong>ID:</strong> {pedidoCriado.id}</p>
            <p><strong>Cliente:</strong> {pedidoCriado.nomeCliente}</p>
            <p><strong>Status:</strong> Aguardando</p>
            <p><strong>Data:</strong> {new Date(pedidoCriado.dataCriacao).toLocaleString('pt-BR')}</p>
          </PedidoInfo>

          <div style={{ marginBottom: '20px' }}>
            <h4>📱 Acompanhe seu pedido:</h4>
            <p>Escaneie o QR Code abaixo ou salve o link para acompanhar o status do seu pedido:</p>
          </div>

          <QRContainer>
            <QRCodeSVG 
              value={gerarUrlAcompanhamento(pedidoCriado.id)}
              size={200}
              level="M"
              includeMargin={true}
            />
          </QRContainer>

          <div style={{ 
            background: '#f8f9fa', 
            padding: '15px', 
            borderRadius: '8px', 
            marginBottom: '20px',
            fontSize: '14px'
          }}>
            <strong>Link de acompanhamento:</strong><br/>
            <a 
              href={gerarUrlAcompanhamento(pedidoCriado.id)}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: '#667eea', wordBreak: 'break-all' }}
            >
              {gerarUrlAcompanhamento(pedidoCriado.id)}
            </a>
          </div>

          <Button onClick={() => {
            setPedidoCriado(null);
            setFormData({
              nomeCliente: '',
              telefone: '',
              email: '',
              itens: '',
              observacoes: ''
            });
          }}>
            Fazer Novo Pedido
          </Button>
        </SuccessCard>
      </Container>
    );
  }

  return (
    <Container>
      <Card>
        <Title>📝 Fazer Pedido</Title>
        
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>Nome Completo *</Label>
            <Input
              type="text"
              name="nomeCliente"
              value={formData.nomeCliente}
              onChange={handleInputChange}
              placeholder="Digite seu nome completo"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>Telefone *</Label>
            <Input
              type="tel"
              name="telefone"
              value={formData.telefone}
              onChange={handleInputChange}
              placeholder="(11) 99999-9999"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>E-mail</Label>
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
            />
          </FormGroup>

          <FormGroup>
            <Label>Itens do Pedido *</Label>
            <TextArea
              name="itens"
              value={formData.itens}
              onChange={handleInputChange}
              placeholder="Descreva os itens que deseja pedir..."
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>Observações</Label>
            <TextArea
              name="observacoes"
              value={formData.observacoes}
              onChange={handleInputChange}
              placeholder="Observações adicionais (opcional)"
            />
          </FormGroup>

          <Button 
            type="submit" 
            disabled={!isFormValid || loading}
          >
            {loading ? 'Criando Pedido...' : 'Criar Pedido'}
          </Button>
        </Form>
      </Card>
    </Container>
  );
};

export default FazerPedido;
