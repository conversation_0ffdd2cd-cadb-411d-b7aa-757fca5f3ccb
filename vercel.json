{"rewrites": [{"source": "/api/check-connection", "destination": "https://chamador.onrender.com/api/check-connection"}, {"source": "/api/login", "destination": "https://chamador.onrender.com/api/login"}, {"source": "/api/logout", "destination": "https://chamador.onrender.com/api/logout"}, {"source": "/api/me", "destination": "https://chamador.onrender.com/api/me"}, {"source": "/api/limpar-dados", "destination": "https://chamador.onrender.com/api/limpar-dados"}, {"source": "/api/limpar-dados-emergencia", "destination": "https://chamador.onrender.com/api/limpar-dados-emergencia"}, {"source": "/api/:path*", "destination": "https://chamador.onrender.com/api/:path*"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Max-Age", "value": "86400"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Max-Age", "value": "86400"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, proxy-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}]}], "regions": ["gru1"], "functions": {"api/**/*.js": {"memory": 1024, "maxDuration": 10}}}