# Configuração do Backend no Render

Este documento fornece instruções para configurar corretamente o backend no serviço Render, com foco especial nas configurações de CORS.

## Variáveis de Ambiente

As seguintes variáveis de ambiente devem ser configuradas no dashboard do Render:

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `CORS_ALLOWED_ORIGINS` | Lista de origens permitidas separadas por vírgula | `https://chamador-git-main-sandro-denis-projects.vercel.app,https://chamador.vercel.app,http://localhost:3000` |
| `PORT` | Porta em que o servidor irá rodar | `3005` |
| `NODE_ENV` | Ambiente de execução | `production` |
| `MONGODB_URI` | URI de conexão com MongoDB | `mongodb+srv://usuario:<EMAIL>/database` |
| `JWT_SECRET` | Chave secreta para tokens JWT | `sua_chave_secreta_supersegura` |

## Configuração do CORS

A configuração do CORS já está implementada no código para usar a variável de ambiente `CORS_ALLOWED_ORIGINS`. Esta variável deve incluir todas as origens que precisam ter acesso à API, incluindo:

1. URL do frontend em produção (Vercel)
2. URLs de preview do frontend (Vercel preview deployments)
3. URLs de desenvolvimento local

### Como configurar no Render

1. Acesse o [Dashboard do Render](https://dashboard.render.com/)
2. Selecione seu serviço de backend
3. Na barra lateral, clique em "Environment"
4. Adicione a variável `CORS_ALLOWED_ORIGINS` com todas as origens permitidas
5. Clique em "Save Changes"
6. O serviço será reiniciado automaticamente com as novas configurações

### Verificando a Configuração CORS

Para verificar se as configurações de CORS estão funcionando corretamente, acesse as seguintes rotas:

- `https://chamador.onrender.com/api/cors-test` - Retorna informações detalhadas sobre a configuração CORS
- `https://chamador.onrender.com/api/check-connection` - Verifica se a conexão está funcionando 

### Problemas Comuns e Soluções

| Problema | Solução |
|----------|---------|
| Erro "No 'Access-Control-Allow-Origin' header" | Verifique se a origem do frontend está incluída em `CORS_ALLOWED_ORIGINS` |
| Erro em requisições OPTIONS (preflight) | Confirme que o servidor responde corretamente a requisições OPTIONS |
| Erro com headers personalizados | Os headers customizados já estão configurados no middleware CORS |

## Rota Emergencial

A rota `/api/limpar-dados-emergencia` foi configurada como pública e responde a requisições de qualquer origem permitida na lista `CORS_ALLOWED_ORIGINS`.

## Logs e Diagnóstico

O servidor foi configurado para registrar logs detalhados sobre requisições CORS. Você pode revisar estes logs no dashboard do Render na seção "Logs". 