import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  TextField,
  Button,
  Paper
} from '@mui/material';

const ThemeSettings = () => {
  const [colors, setColors] = useState({
    headerBg: '#1976d2',
    headerText: '#ffffff',
    footerBg: '#f5f5f5',
    footerText: '#000000',
    itemBg: '#ffffff',
    itemText: '#000000',
    typeNormal: '#4caf50',
    typePrioritario: '#f44336',
    typeIdoso: '#2196f3',
    typeDeficiente: '#ff9800'
  });

  useEffect(() => {
    // Carregar cores salvas do localStorage
    const savedColors = localStorage.getItem('themeColors');
    if (savedColors) {
      setColors(JSON.parse(savedColors));
      applyColors(JSON.parse(savedColors));
    }
  }, []);

  const handleColorChange = (key, value) => {
    const newColors = { ...colors, [key]: value };
    setColors(newColors);
    localStorage.setItem('themeColors', JSON.stringify(newColors));
    applyColors(newColors);
  };

  const applyColors = (colorObj) => {
    Object.entries(colorObj).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--${key}`, value);
    });
  };

  const resetColors = () => {
    const defaultColors = {
      headerBg: '#1976d2',
      headerText: '#ffffff',
      footerBg: '#f5f5f5',
      footerText: '#000000',
      itemBg: '#ffffff',
      itemText: '#000000',
      typeNormal: '#4caf50',
      typePrioritario: '#f44336',
      typeIdoso: '#2196f3',
      typeDeficiente: '#ff9800'
    };
    setColors(defaultColors);
    localStorage.setItem('themeColors', JSON.stringify(defaultColors));
    applyColors(defaultColors);
  };

  return (
    <Paper elevation={3} sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        Configurações de Tema
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Cabeçalho
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Cor de Fundo"
                type="color"
                value={colors.headerBg}
                onChange={(e) => handleColorChange('headerBg', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Cor do Texto"
                type="color"
                value={colors.headerText}
                onChange={(e) => handleColorChange('headerText', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Rodapé
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Cor de Fundo"
                type="color"
                value={colors.footerBg}
                onChange={(e) => handleColorChange('footerBg', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Cor do Texto"
                type="color"
                value={colors.footerText}
                onChange={(e) => handleColorChange('footerText', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Itens
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Cor de Fundo"
                type="color"
                value={colors.itemBg}
                onChange={(e) => handleColorChange('itemBg', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Cor do Texto"
                type="color"
                value={colors.itemText}
                onChange={(e) => handleColorChange('itemText', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Tipos de Senha
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Normal"
                type="color"
                value={colors.typeNormal}
                onChange={(e) => handleColorChange('typeNormal', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Prioritário"
                type="color"
                value={colors.typePrioritario}
                onChange={(e) => handleColorChange('typePrioritario', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Idoso"
                type="color"
                value={colors.typeIdoso}
                onChange={(e) => handleColorChange('typeIdoso', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Deficiente"
                type="color"
                value={colors.typeDeficiente}
                onChange={(e) => handleColorChange('typeDeficiente', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <Button 
              variant="contained" 
              color="secondary" 
              onClick={resetColors}
            >
              Restaurar Cores Padrão
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ThemeSettings; 