{"name": "chamadorfront", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebase/auth": "^1.9.1", "@firebase/firestore": "^4.7.10", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase": "^11.5.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "print-js": "^1.6.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^6.20.0", "styled-components": "^6.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0"}}