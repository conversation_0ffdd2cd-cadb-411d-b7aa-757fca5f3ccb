import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';
import axios from '../config/api';
import { FaUserPlus, FaEdit, FaTrash, <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaUserShield, FaCog } from 'react-icons/fa';
import PermissionsManager from '../components/PermissionsManager';

// Estilos
const AdminContainer = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin-bottom: 20px;
`;

const Card = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;

  th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }

  th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: bold;
  }

  tr:hover {
    background-color: #f5f5f5;
  }
`;

const Button = styled.button`
  background-color: ${props => {
    if (props.$danger) return '#e74c3c';
    if (props.$warning) return '#f39c12';
    if (props.$success) return '#2ecc71';
    if (props.$info) return '#3498db';
    return '#3498db';
  }};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  margin-right: 5px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;

  &:hover {
    background-color: ${props => {
      if (props.$danger) return '#c0392b';
      if (props.$warning) return '#e67e22';
      if (props.$success) return '#27ae60';
      if (props.$info) return '#2980b9';
      return '#2980b9';
    }};
  }

  svg {
    margin-right: 5px;
  }

  &:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 5px;
`;

const FormGroup = styled.div`
  margin-bottom: 15px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #2c3e50;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;

  &:focus {
    border-color: #3498db;
    outline: none;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #2c3e50;
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
`;

const ErrorMessage = styled.div`
  color: #e74c3c;
  background-color: #fadbd8;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
`;

const SuccessMessage = styled.div`
  color: #27ae60;
  background-color: #d5f5e3;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
`;

const LoadingSpinner = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  svg {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Admin = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Estados para modais
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);

  // Estado para o usuário selecionado para edição/exclusão
  const [selectedUser, setSelectedUser] = useState(null);

  // Estados para formulários
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    companyName: '',
    newPassword: ''
  });

  // Verificar se o usuário é administrador
  useEffect(() => {
    if (!user || user.email !== '<EMAIL>') {
      navigate('/');
    }
  }, [user, navigate]);

  // Carregar lista de usuários
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const response = await axios.get('/api/admin/users', {
        headers: { Authorization: `Bearer ${token}` }
      });

      setUsers(response.data);
    } catch (err) {
      console.error('Erro ao carregar usuários:', err);
      setError('Não foi possível carregar a lista de usuários. Verifique se você tem permissão de administrador.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // Manipuladores de formulário
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Criar usuário
  const handleCreateUser = async () => {
    try {
      setLoading(true);
      setError(null);

      const { email, password, companyName } = formData;

      if (!email || !password || !companyName) {
        setError('Todos os campos são obrigatórios');
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('token');
      await axios.post('/api/admin/users', {
        email,
        password,
        companyName
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setSuccess('Usuário criado com sucesso!');
      setShowCreateModal(false);
      setFormData({
        email: '',
        password: '',
        companyName: '',
        newPassword: ''
      });

      // Recarregar a lista de usuários
      loadUsers();

      // Limpar mensagem de sucesso após 3 segundos
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Erro ao criar usuário:', err);
      setError(err.response?.data?.message || 'Erro ao criar usuário');
    } finally {
      setLoading(false);
    }
  };

  // Editar usuário
  const handleEditUser = async () => {
    try {
      setLoading(true);
      setError(null);

      const { email, companyName } = formData;

      if (!email || !companyName) {
        setError('Email e nome da empresa são obrigatórios');
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('token');
      await axios.put(`/api/admin/users/${selectedUser._id}`, {
        email,
        companyName
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setSuccess('Usuário atualizado com sucesso!');
      setShowEditModal(false);

      // Recarregar a lista de usuários
      loadUsers();

      // Limpar mensagem de sucesso após 3 segundos
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Erro ao atualizar usuário:', err);
      setError(err.response?.data?.message || 'Erro ao atualizar usuário');
    } finally {
      setLoading(false);
    }
  };

  // Redefinir senha
  const handleResetPassword = async () => {
    try {
      setLoading(true);
      setError(null);

      const { newPassword } = formData;

      if (!newPassword || newPassword.length < 6) {
        setError('A nova senha deve ter pelo menos 6 caracteres');
        setLoading(false);
        return;
      }

      const token = localStorage.getItem('token');
      await axios.post(`/api/admin/users/${selectedUser._id}/reset-password`, {
        newPassword
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setSuccess('Senha redefinida com sucesso!');
      setShowResetPasswordModal(false);
      setFormData(prev => ({ ...prev, newPassword: '' }));

      // Limpar mensagem de sucesso após 3 segundos
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Erro ao redefinir senha:', err);
      setError(err.response?.data?.message || 'Erro ao redefinir senha');
    } finally {
      setLoading(false);
    }
  };

  // Excluir usuário
  const handleDeleteUser = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      await axios.delete(`/api/admin/users/${selectedUser._id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setSuccess('Usuário excluído com sucesso!');
      setShowDeleteModal(false);

      // Recarregar a lista de usuários
      loadUsers();

      // Limpar mensagem de sucesso após 3 segundos
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Erro ao excluir usuário:', err);
      setError(err.response?.data?.message || 'Erro ao excluir usuário');
    } finally {
      setLoading(false);
    }
  };

  // Abrir modal de edição
  const openEditModal = (user) => {
    setSelectedUser(user);
    setFormData({
      email: user.email,
      companyName: user.companyName,
      password: '',
      newPassword: ''
    });
    setShowEditModal(true);
  };

  // Abrir modal de redefinição de senha
  const openResetPasswordModal = (user) => {
    setSelectedUser(user);
    setFormData(prev => ({ ...prev, newPassword: '' }));
    setShowResetPasswordModal(true);
  };

  // Abrir modal de exclusão
  const openDeleteModal = (user) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  // Abrir modal de permissões
  const openPermissionsModal = (user) => {
    setSelectedUser(user);
    setShowPermissionsModal(true);
  };

  // Salvar permissões
  const handleSavePermissions = async (permissions) => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      await axios.put(`/api/admin/users/${selectedUser._id}`, {
        permissoes: permissions
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setSuccess('Permissões atualizadas com sucesso!');
      setShowPermissionsModal(false);
      setSelectedUser(null);
      await loadUsers();
    } catch (err) {
      console.error('Erro ao atualizar permissões:', err);
      setError('Erro ao atualizar permissões. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  // Renderizar modais
  const renderCreateModal = () => (
    <Modal>
      <ModalContent>
        <ModalHeader>
          <h2>Criar Novo Usuário</h2>
          <Button $danger onClick={() => setShowCreateModal(false)}>X</Button>
        </ModalHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <FormGroup>
          <Label htmlFor="email">Email</Label>
          <Input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Email do usuário"
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="password">Senha</Label>
          <Input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="Senha do usuário"
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="companyName">Nome da Empresa</Label>
          <Input
            type="text"
            id="companyName"
            name="companyName"
            value={formData.companyName}
            onChange={handleInputChange}
            placeholder="Nome da empresa"
          />
        </FormGroup>

        <ModalFooter>
          <Button $danger onClick={() => setShowCreateModal(false)}>Cancelar</Button>
          <Button $success onClick={handleCreateUser} disabled={loading}>
            {loading ? <FaSpinner /> : <FaUserPlus />} Criar Usuário
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );

  const renderEditModal = () => (
    <Modal>
      <ModalContent>
        <ModalHeader>
          <h2>Editar Usuário</h2>
          <Button $danger onClick={() => setShowEditModal(false)}>X</Button>
        </ModalHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <FormGroup>
          <Label htmlFor="email">Email</Label>
          <Input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Email do usuário"
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="companyName">Nome da Empresa</Label>
          <Input
            type="text"
            id="companyName"
            name="companyName"
            value={formData.companyName}
            onChange={handleInputChange}
            placeholder="Nome da empresa"
          />
        </FormGroup>

        <ModalFooter>
          <Button $danger onClick={() => setShowEditModal(false)}>Cancelar</Button>
          <Button $success onClick={handleEditUser} disabled={loading}>
            {loading ? <FaSpinner /> : <FaEdit />} Salvar Alterações
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );

  const renderResetPasswordModal = () => (
    <Modal>
      <ModalContent>
        <ModalHeader>
          <h2>Redefinir Senha</h2>
          <Button $danger onClick={() => setShowResetPasswordModal(false)}>X</Button>
        </ModalHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <p>Redefinindo senha para o usuário: <strong>{selectedUser?.email}</strong></p>

        <FormGroup>
          <Label htmlFor="newPassword">Nova Senha</Label>
          <Input
            type="password"
            id="newPassword"
            name="newPassword"
            value={formData.newPassword}
            onChange={handleInputChange}
            placeholder="Nova senha"
          />
        </FormGroup>

        <ModalFooter>
          <Button $danger onClick={() => setShowResetPasswordModal(false)}>Cancelar</Button>
          <Button $warning onClick={handleResetPassword} disabled={loading}>
            {loading ? <FaSpinner /> : <FaKey />} Redefinir Senha
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );

  const renderDeleteModal = () => (
    <Modal>
      <ModalContent>
        <ModalHeader>
          <h2>Excluir Usuário</h2>
          <Button $danger onClick={() => setShowDeleteModal(false)}>X</Button>
        </ModalHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <p>Tem certeza que deseja excluir o usuário <strong>{selectedUser?.email}</strong>?</p>
        <p>Esta ação não pode ser desfeita e também excluirá todas as senhas associadas a este usuário.</p>

        <ModalFooter>
          <Button $info onClick={() => setShowDeleteModal(false)}>Cancelar</Button>
          <Button $danger onClick={handleDeleteUser} disabled={loading}>
            {loading ? <FaSpinner /> : <FaTrash />} Excluir Usuário
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );

  const renderPermissionsModal = () => (
    <Modal>
      <ModalContent style={{ maxWidth: '900px', maxHeight: '80vh', overflow: 'auto' }}>
        <ModalHeader>
          <h2>Gerenciar Permissões</h2>
          <Button $danger onClick={() => setShowPermissionsModal(false)}>X</Button>
        </ModalHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <PermissionsManager
          user={selectedUser}
          onSave={handleSavePermissions}
          loading={loading}
        />
      </ModalContent>
    </Modal>
  );

  // Renderização principal
  return (
    <AdminContainer>
      <Title>Painel de Administração</Title>

      {success && <SuccessMessage>{success}</SuccessMessage>}
      {error && <ErrorMessage>{error}</ErrorMessage>}

      <Card>
        <Button $success onClick={() => setShowCreateModal(true)}>
          <FaUserPlus /> Criar Novo Usuário
        </Button>

        {loading && !users.length ? (
          <LoadingSpinner>
            <FaSpinner size={30} />
          </LoadingSpinner>
        ) : (
          <Table>
            <thead>
              <tr>
                <th>Email</th>
                <th>Empresa</th>
                <th>Data de Criação</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user => (
                <tr key={user._id}>
                  <td>{user.email}</td>
                  <td>{user.companyName}</td>
                  <td>{new Date(user.createdAt).toLocaleString()}</td>
                  <td>
                    <ActionButtons>
                      <Button $info onClick={() => openEditModal(user)}>
                        <FaEdit /> Editar
                      </Button>
                      <Button $success onClick={() => openPermissionsModal(user)}>
                        <FaUserShield /> Permissões
                      </Button>
                      <Button $warning onClick={() => openResetPasswordModal(user)}>
                        <FaKey /> Senha
                      </Button>
                      <Button
                        $danger
                        onClick={() => openDeleteModal(user)}
                        disabled={user.email === '<EMAIL>'}
                      >
                        <FaTrash /> Excluir
                      </Button>
                    </ActionButtons>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        )}
      </Card>

      {showCreateModal && renderCreateModal()}
      {showEditModal && renderEditModal()}
      {showResetPasswordModal && renderResetPasswordModal()}
      {showDeleteModal && renderDeleteModal()}
      {showPermissionsModal && renderPermissionsModal()}
    </AdminContainer>
  );
};

export default Admin;