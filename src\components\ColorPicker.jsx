import React from 'react';
import { HexColorPicker } from 'react-colorful';
import { Popover } from '@mui/material';

const ColorPicker = ({ color, onChange }) => {
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <div>
      <div
        onClick={handleClick}
        style={{
          width: '36px',
          height: '36px',
          borderRadius: '4px',
          backgroundColor: color,
          cursor: 'pointer',
          border: '1px solid #ccc'
        }}
      />
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <HexColorPicker color={color} onChange={onChange} />
      </Popover>
    </div>
  );
};

export default ColorPicker; 