import express from 'express';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import verificarAdmin from '../middleware/adminMiddleware.js';

const router = express.Router();

// Middleware para verificar se o usuário é administrador
router.use(verificarAdmin);

// Rota para listar todos os usuários
router.get('/users', async (req, res) => {
  try {
    const db = req.app.locals.db;
    if (!db) {
      return res.status(500).json({ message: 'Erro de conexão com o banco de dados' });
    }

    // Buscar todos os usuários, excluindo a senha da resposta
    const users = await db.collection('users').find({}).project({ password: 0 }).toArray();
    console.log(`Administrador ${req.userId} listou ${users.length} usuários`);
    
    res.json(users);
  } catch (error) {
    console.error('Erro ao listar usuários:', error);
    res.status(500).json({ 
      message: 'Erro ao listar usuários',
      error: error.message 
    });
  }
});

// Rota para obter um usuário específico
router.get('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const db = req.app.locals.db;
    
    // Buscar o usuário pelo ID, excluindo a senha da resposta
    const user = await db.collection('users').findOne(
      { _id: id },
      { projection: { password: 0 } }
    );
    
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    console.log(`Administrador ${req.userId} consultou o usuário ${id}`);
    res.json(user);
  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    res.status(500).json({ 
      message: 'Erro ao buscar usuário',
      error: error.message 
    });
  }
});

// Rota para criar um novo usuário
router.post('/users', async (req, res) => {
  try {
    const { email, password, companyName } = req.body;
    const db = req.app.locals.db;
    
    // Validar campos obrigatórios
    if (!email || !password || !companyName) {
      return res.status(400).json({ 
        message: 'Email, senha e nome da empresa são obrigatórios' 
      });
    }
    
    // Verificar se o email já está em uso
    const existingUser = await db.collection('users').findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'Email já cadastrado' });
    }
    
    // Criar o novo usuário
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = {
      _id: uuidv4(),
      email,
      companyName,
      password: hashedPassword,
      createdAt: new Date().toISOString(),
      permissoes: {
        gerarSenha: true,
        chamarSenha: true,
        finalizarSenha: true
      },
      config: {
        theme: 'light',
        backgroundColor: '#f8f9fa',
        textColor: '#2c3e50',
        senhaColor: '#3498db',
        fontFamily: 'Arial',
        fontSize: 120,
        logo: null,
        backgroundType: 'color',
        backgroundImage: null,
        footerText: '',
        voiceType: 'default',
        volume: 80,
        soundEffect: 'bell',
        repeatInterval: 1,
        // Configuração de tipos de senha personalizados
        tiposSenha: {
          P: {
            nome: 'Prioritária',
            descricao: 'Senha Prioritária',
            cor: '#e74c3c',
            ativo: true
          },
          N: {
            nome: 'Normal',
            descricao: 'Senha Normal',
            cor: '#3498db',
            ativo: true
          },
          R: {
            nome: 'Rápido',
            descricao: 'Atendimento Rápido',
            cor: '#2ecc71',
            ativo: true
          }
        }
      }
    };
    
    await db.collection('users').insertOne(user);
    
    // Remover a senha do objeto de resposta
    const { password: _, ...userWithoutPassword } = user;
    
    console.log(`Administrador ${req.userId} criou um novo usuário: ${user._id}`);
    res.status(201).json(userWithoutPassword);
  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    res.status(500).json({ 
      message: 'Erro ao criar usuário',
      error: error.message 
    });
  }
});

// Rota para atualizar um usuário existente
router.put('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { email, companyName, permissoes, config } = req.body;
    const db = req.app.locals.db;
    
    // Verificar se o usuário existe
    const existingUser = await db.collection('users').findOne({ _id: id });
    if (!existingUser) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Verificar se o novo email já está em uso por outro usuário
    if (email && email !== existingUser.email) {
      const emailExists = await db.collection('users').findOne({ 
        email, 
        _id: { $ne: id } 
      });
      
      if (emailExists) {
        return res.status(400).json({ message: 'Email já está em uso por outro usuário' });
      }
    }
    
    // Preparar objeto de atualização
    const updates = {};
    
    if (email) updates.email = email;
    if (companyName) updates.companyName = companyName;
    if (permissoes) updates.permissoes = permissoes;
    if (config) updates.config = config;
    
    // Atualizar o usuário
    await db.collection('users').updateOne(
      { _id: id },
      { $set: updates }
    );
    
    // Buscar o usuário atualizado
    const updatedUser = await db.collection('users').findOne(
      { _id: id },
      { projection: { password: 0 } }
    );
    
    console.log(`Administrador ${req.userId} atualizou o usuário ${id}`);
    res.json(updatedUser);
  } catch (error) {
    console.error('Erro ao atualizar usuário:', error);
    res.status(500).json({ 
      message: 'Erro ao atualizar usuário',
      error: error.message 
    });
  }
});

// Rota para redefinir a senha de um usuário
router.post('/users/:id/reset-password', async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;
    const db = req.app.locals.db;
    
    // Validar a nova senha
    if (!newPassword || newPassword.length < 6) {
      return res.status(400).json({ 
        message: 'A nova senha deve ter pelo menos 6 caracteres' 
      });
    }
    
    // Verificar se o usuário existe
    const existingUser = await db.collection('users').findOne({ _id: id });
    if (!existingUser) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Gerar hash da nova senha
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Atualizar a senha do usuário
    await db.collection('users').updateOne(
      { _id: id },
      { $set: { password: hashedPassword } }
    );
    
    console.log(`Administrador ${req.userId} redefiniu a senha do usuário ${id}`);
    res.json({ message: 'Senha redefinida com sucesso' });
  } catch (error) {
    console.error('Erro ao redefinir senha:', error);
    res.status(500).json({ 
      message: 'Erro ao redefinir senha',
      error: error.message 
    });
  }
});

// Rota para excluir um usuário
router.delete('/users/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const db = req.app.locals.db;
    
    // Verificar se o usuário existe
    const existingUser = await db.collection('users').findOne({ _id: id });
    if (!existingUser) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Não permitir que o administrador exclua a si mesmo
    if (existingUser.email === '<EMAIL>') {
      return res.status(403).json({ 
        message: 'Não é permitido excluir a conta de administrador' 
      });
    }
    
    // Excluir o usuário
    await db.collection('users').deleteOne({ _id: id });
    
    // Opcionalmente, excluir também as senhas associadas ao usuário
    await db.collection('senhas').deleteMany({ userId: id });
    
    console.log(`Administrador ${req.userId} excluiu o usuário ${id}`);
    res.json({ message: 'Usuário excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir usuário:', error);
    res.status(500).json({ 
      message: 'Erro ao excluir usuário',
      error: error.message 
    });
  }
});

export default router;