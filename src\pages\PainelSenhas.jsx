import React, { useState, useEffect } from 'react'
import styled, { css } from 'styled-components'
import { useSenha } from '../context/SenhaContext'
import layoutThemes from '../themes/layoutThemes'
import { useAuth } from '../context/AuthContext'

// Atualizar para usar o mesmo padrão do resto da aplicação
const STORAGE_KEY_BASE = 'senhas_sistema'

const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: ${props => props.$backgroundColor || '#f8f9fa'};
  background-image: ${props => props.$backgroundImage ? `url(${props.$backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  overflow: hidden;
  font-family: ${props => props.$fontFamily || 'Arial'};
  transition: all 0.3s ease;
  color: ${props => props.$textColor || '#333'};
  
  /* Aplicar tema de layout se fornecido */
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.bg && css`
    background-color: ${layoutThemes[props.$layoutTheme].colors.bg};
    color: ${layoutThemes[props.$layoutTheme].colors.text || props.$textColor || '#333'};
  `}
  
  /* Aplicar configuração personalizada se não estiver usando tema de layout ou se estiver usando backgroundType */
  ${props => (!props.$layoutTheme || props.$backgroundType === 'color') && css`
    background-color: ${props.$backgroundColor};
  `}
  
  /* Aplicar CSS personalizado se fornecido */
  ${props => props.$customCss && css`${props.$customCss}`}
`

const Header = styled.div`
  background: ${props => props.$headerColor || 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)'};
  color: ${props => props.$headerTextColor || 'white'};
  padding: 15px 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  
  /* Removido o sublinhado colorido para atender à customização do cliente */
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.header && css`
    background: ${layoutThemes[props.$layoutTheme].colors.header};
    color: ${layoutThemes[props.$layoutTheme].colors.headerText};
  `}
`

const Title = styled.h1`
  font-size: 32px;
  margin: 0;
  position: relative;
  padding: 0 15px;
  
  /* Removido o sublinhado colorido para atender à customização do cliente */
`

const PainelContainer = styled.div`
  display: flex;
  flex: 1;
  padding: 20px;
  gap: 20px;
  overflow: hidden;
  flex-wrap: wrap;
  transition: all 0.3s ease;
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.layout?.painelContainer && css`
    display: ${layoutThemes[props.$layoutTheme].layout.painelContainer.display || 'flex'};
    flex-direction: ${layoutThemes[props.$layoutTheme].layout.painelContainer.flexDirection || 'row'};
    flex-wrap: ${layoutThemes[props.$layoutTheme].layout.painelContainer.flexWrap || 'wrap'};
    gap: ${layoutThemes[props.$layoutTheme].layout.painelContainer.gap || '20px'};
    grid-template-columns: ${layoutThemes[props.$layoutTheme].layout.painelContainer.gridTemplateColumns || 'none'};
    grid-template-rows: ${layoutThemes[props.$layoutTheme].layout.painelContainer.gridTemplateRows || 'none'};
    padding: ${layoutThemes[props.$layoutTheme].layout.painelContainer.padding || '20px'};
    height: ${layoutThemes[props.$layoutTheme].layout.painelContainer.height || 'auto'};
  `}
`

const SenhaAtualContainer = styled.div`
  flex: 2;
  background-color: ${props => {
    // Se estiver usando tema de layout
    if (props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.senhaAtual?.bg) {
      return layoutThemes[props.$layoutTheme].colors.senhaAtual.bg;
    }
    // Se estiver usando configuração personalizada
    else if (props.$senhaAtualBgColor) {
      return props.$senhaAtualBgColor;
    }
    // Fallback para cor padrão
    else {
      return 'white';
    }
  }};
  border-radius: ${props => props.$borderRadius || '12px'};
  box-shadow: ${props => props.$boxShadow || '0 8px 24px rgba(0, 0, 0, 0.15)'};
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  min-width: 300px;
  border: ${props => props.$borderStyle ? `${props.$borderWidth || '1px'} ${props.$borderStyle} ${props.$borderColor || '#ddd'}` : 'none'};
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }
  
  /* Removido o sublinhado colorido para atender à customização do cliente */
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.layout?.senhaAtualContainer && css`
    flex: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.flex || '2'};
    grid-column: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.gridColumn || 'auto'};
    grid-row: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.gridRow || 'auto'};
    padding: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.padding || '25px'};
    min-width: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.minWidth || '300px'};
    border: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.border || 'none'};
    border-radius: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.borderRadius || '12px'};
    box-shadow: ${layoutThemes[props.$layoutTheme].layout.senhaAtualContainer.boxShadow || '0 8px 24px rgba(0, 0, 0, 0.15)'};
  `}
`

const SenhaAtualTitle = styled.h2`
  font-size: ${props => props.$fontSize || '24px'};
  margin-bottom: 20px;
  color: ${props => {
    // Se estiver usando tema de layout
    if (props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.senhaAtual?.text) {
      return layoutThemes[props.$layoutTheme].colors.senhaAtual.text;
    }
    // Se estiver usando configuração personalizada
    else if (props.$textColor) {
      return props.$textColor;
    }
    // Fallback para cor padrão
    else {
      return '#2c3e50';
    }
  }};
  font-family: ${props => props.$fontFamily || 'Arial'};
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.fonts?.titulo && css`
    font-size: ${layoutThemes[props.$layoutTheme].fonts.titulo.size || 24}px;
    font-family: ${layoutThemes[props.$layoutTheme].fonts.titulo.family || 'Arial'};
  `}
`

const SenhaAtualNumero = styled.div`
  font-size: ${props => props.$fontSize || 120}px;
  font-weight: bold;
  margin: 20px 0;
  color: ${props => {
    // Se estiver usando tema de layout
    if (props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.senhaAtual?.numero) {
      return props.$tipo === 'P' ? layoutThemes[props.$layoutTheme].colors.senhaAtual.numero : 
             props.$tipo === 'N' ? layoutThemes[props.$layoutTheme].colors.senhaAtual.numero : 
             props.$tipo === 'R' ? layoutThemes[props.$layoutTheme].colors.senhaAtual.numero : 
             layoutThemes[props.$layoutTheme].colors.senhaAtual.numero;
    }
    // Se estiver usando configuração personalizada
    else if (props.$senhaColor) {
      return props.$senhaColor;
    }
    // Fallback para cores padrão por tipo
    else {
      return props.$tipo === 'P' ? props.$tipoColors?.P || '#e74c3c' : 
             props.$tipo === 'N' ? props.$tipoColors?.N || '#3498db' : 
             props.$tipo === 'R' ? props.$tipoColors?.R || '#2ecc71' : 
             props.$tipoColors?.default || '#7f8c8d';
    }
  }};
  text-shadow: ${props => props.$textShadow || '0 4px 8px rgba(0, 0, 0, 0.1)'};
  transition: transform 0.2s ease;
  font-family: ${props => props.$fontFamily || 'Arial'};
  
  &:hover {
    transform: scale(1.05);
  }
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.fonts?.senhaAtual && css`
    font-size: ${layoutThemes[props.$layoutTheme].fonts.senhaAtual.size || 120}px;
    font-family: ${layoutThemes[props.$layoutTheme].fonts.senhaAtual.family || 'Arial'};
  `}
`

const SenhaAtualTipo = styled.div`
  font-size: 32px;
  margin-bottom: 15px;
  color: ${props => props.$textColor || '#7f8c8d'};
`

const GuicheInfo = styled.div`
  font-size: 48px;
  font-weight: bold;
  margin: 15px 0;
  color: ${props => props.$textColor || '#2c3e50'};
`

const SenhasContainer = styled.div`
  flex: 1;
  background-color: ${props => {
    // Se estiver usando tema de layout
    if (props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.ultimasSenhas?.bg) {
      return layoutThemes[props.$layoutTheme].colors.ultimasSenhas.bg;
    }
    // Se estiver usando configuração personalizada
    else if (props.$senhasListBgColor) {
      return props.$senhasListBgColor;
    }
    // Fallback para cor padrão
    else {
      return 'white';
    }
  }};
  border-radius: ${props => props.$borderRadius || '12px'};
  box-shadow: ${props => props.$boxShadow || '0 8px 24px rgba(0, 0, 0, 0.15)'};
  padding: 25px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  min-width: 300px;
  border: ${props => props.$borderStyle ? `${props.$borderWidth || '1px'} ${props.$borderStyle} ${props.$borderColor || '#ddd'}` : 'none'};
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }
  
  /* Removido o sublinhado colorido para atender à customização do cliente */
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.layout?.senhasListContainer && css`
    flex: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.flex || '1'};
    grid-column: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.gridColumn || 'auto'};
    grid-row: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.gridRow || 'auto'};
    display: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.display || 'flex'};
    flex-direction: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.flexDirection || 'column'};
    gap: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.gap || 'inherit'};
    min-width: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.minWidth || '300px'};
    grid-template-columns: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.gridTemplateColumns || 'none'};
    border-radius: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.borderRadius || '12px'};
    box-shadow: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.boxShadow || '0 8px 24px rgba(0, 0, 0, 0.15)'};
    border: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.border || 'none'};
    padding: ${layoutThemes[props.$layoutTheme].layout.senhasListContainer.padding || '25px'};
  `}
`

const UltimasSenhasContainer = styled(SenhasContainer)``

const SenhasAguardandoContainer = styled(SenhasContainer)``

const UltimasSenhasTitle = styled.h2`
  font-size: ${props => props.$fontSize || '24px'};
  margin-bottom: 20px;
  color: ${props => props.$textColor || '#2c3e50'};
  text-align: center;
  position: relative;
  padding-bottom: 15px;
  font-family: ${props => props.$fontFamily || 'Arial'};
  
  /* Removido o sublinhado colorido para atender à customização do cliente */
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.fonts?.titulo && css`
    font-size: ${layoutThemes[props.$layoutTheme].fonts.titulo.size || 24}px;
    font-family: ${layoutThemes[props.$layoutTheme].fonts.titulo.family || 'Arial'};
  `}
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.ultimasSenhas && css`
    color: ${layoutThemes[props.$layoutTheme].colors.ultimasSenhas.titulo || '#2c3e50'};
  `}
`

const SenhasList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  max-height: 300px;
  flex: 1;
`

const SenhaItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: ${props => props.$itemBorderRadius || '8px'};
  background-color: ${props => props.$itemBgColor || '#f8f9fa'};
  border-left: 5px solid ${props => {
    switch(props.$tipo) {
      case 'P': return props.$tipoColors?.P || '#e74c3c';
      case 'N': return props.$tipoColors?.N || '#3498db';
      case 'R': return props.$tipoColors?.R || '#2ecc71';
      default: return props.$tipoColors?.default || '#7f8c8d';
    }
  }};
  margin-bottom: 10px;
  transition: all 0.2s ease;
  box-shadow: ${props => props.$itemBoxShadow || '0 2px 5px rgba(0, 0, 0, 0.05)'};
  
  &:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.ultimasSenhas && css`
    background-color: ${layoutThemes[props.$layoutTheme].colors.ultimasSenhas.item || '#f8f9fa'};
  `}
`

const SenhaNumero = styled.div`
  font-size: ${props => props.$fontSize || '24px'};
  font-weight: bold;
  font-family: ${props => props.$fontFamily || 'Arial'};
  color: ${props => {
    switch(props.$tipo) {
      case 'P': return props.$tipoColors?.P || '#e74c3c';
      case 'N': return props.$tipoColors?.N || '#3498db';
      case 'R': return props.$tipoColors?.R || '#2ecc71';
      default: return props.$tipoColors?.default || '#7f8c8d';
    }
  }};
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.fonts?.senhaLista && css`
    font-size: ${layoutThemes[props.$layoutTheme].fonts.senhaLista.size || 24}px;
    font-family: ${layoutThemes[props.$layoutTheme].fonts.senhaLista.family || 'Arial'};
  `}
`

const SenhaGuiche = styled.div`
  font-size: 20px;
  color: #2c3e50;
  font-weight: 600;
`

const Footer = styled.div`
  background: ${props => props.$footerColor || 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)'};
  color: ${props => props.$footerTextColor || 'white'};
  padding: 12px;
  text-align: center;
  font-size: 18px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  
  &::after {
    content: ${props => props.$hideBorderAccent ? 'none' : '\'\''} ;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #e74c3c);
    display: ${props => props.$hideBorderAccent ? 'none' : 'block'};
  }
  
  ${props => props.$layoutTheme && layoutThemes[props.$layoutTheme]?.colors?.header && css`
    background: ${layoutThemes[props.$layoutTheme].colors.header};
    color: ${layoutThemes[props.$layoutTheme].colors.headerText};
  `}
`

const Clock = styled.div`
  font-size: 20px;
  font-weight: 500;
`

const PainelSenhas = () => {
  const { TIPOS_SENHA, getSenhasPorStatus } = useSenha()
  const { user } = useAuth()
  const [ultimasSenhas, setUltimasSenhas] = useState([])
  const [senhaAtual, setSenhaAtual] = useState(null)
  const [senhasAguardando, setSenhasAguardando] = useState([])
  const [currentTime, setCurrentTime] = useState(new Date())
  const [config, setConfig] = useState({
    title: 'Painel de Senhas',
    theme: 'light',
    backgroundColor: '#f5f5f5',
    textColor: '#333',
    senhaColor: '#3498db',
    fontFamily: 'Arial',
    fontSize: 120,
    logo: null,
    backgroundType: 'color',
    backgroundImage: null,
    footerText: 'Sistema de Gerenciamento de Senhas',
    layoutTheme: 'default',
    // Novas propriedades de personalização avançada
    senhaAtualBgColor: 'white',
    senhasListBgColor: 'white',
    headerColor: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
    headerTextColor: 'white',
    footerColor: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
    footerTextColor: 'white',
    borderRadius: '12px',
    borderStyle: 'none',
    borderColor: '#ddd',
    borderWidth: '1px',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
    hideBorderAccent: false,
    hideUnderline: false,
    hideReloj: false,
    hideFooter: false,
    textShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    itemBgColor: '#f8f9fa',
    itemBorderRadius: '8px',
    itemBoxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',
    logoPosition: 'center',
    logoSize: 'medium',
    customCss: '',
    tipoColors: {
      P: '#e74c3c',
      N: '#3498db',
      R: '#2ecc71',
      default: '#7f8c8d'
    }
  })
  
  // Carrega configurações salvas e configura listeners
  useEffect(() => {
    const loadConfig = () => {
      try {
        const savedConfig = localStorage.getItem('painelConfig')
        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig)
          console.log('Configurações carregadas:', parsedConfig)
          setConfig(parsedConfig)
        } else {
          // Configurações padrão de áudio
          setConfig(prev => ({
            ...prev,
            voiceType: 'default',
            volume: 80,
            soundEffect: 'bell',
            repeatInterval: 1
          }))
        }
      } catch (error) {
        console.error('Erro ao carregar configurações:', error)
      }
    }

    loadConfig()

    // Adiciona listener para mudanças no localStorage de outras abas
    const handleStorageChange = (e) => {
      if (e.key === 'painelConfig') {
        loadConfig()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Função para reproduzir áudio
  const playAudio = async (audioPath, volume = config.volume) => {
    try {
      const audio = new Audio(audioPath)
      audio.volume = (volume || 80) / 100
      
      // Adiciona timeout para evitar espera infinita
      const loadPromise = new Promise((resolve, reject) => {
        // Evento disparado quando o áudio está pronto para reprodução
        audio.addEventListener('canplaythrough', resolve, { once: true })
        
        // Evento disparado quando ocorre um erro
        audio.addEventListener('error', (e) => {
          console.warn(`Erro ao carregar áudio ${audioPath}:`, e)
          // Resolvemos mesmo com erro para tentar reproduzir de qualquer forma
          resolve()
        }, { once: true })
        
        // Inicia o carregamento
        audio.load()
      })
      
      // Adiciona um timeout para não esperar indefinidamente
      const timeoutPromise = new Promise(resolve => {
        setTimeout(() => {
          console.warn(`Áudio não carregado completamente em 3 segundos: ${audioPath}, tentando mesmo assim`)
          resolve()
        }, 3000)
      })
      
      // Espera o que acontecer primeiro: carregamento completo ou timeout
      await Promise.race([loadPromise, timeoutPromise])
      
      // Tenta reproduzir o áudio mesmo se não estiver totalmente carregado
      try {
        await audio.play()
        return true
      } catch (playError) {
        console.warn(`Falha ao reproduzir áudio ${audioPath}, tentando método alternativo:`, playError)
        
        // Método alternativo: criar um novo elemento de áudio e anexá-lo ao DOM
        const audioElement = document.createElement('audio')
        audioElement.src = audioPath
        audioElement.volume = (volume || 80) / 100
        document.body.appendChild(audioElement)
        
        try {
          await audioElement.play()
          // Remove o elemento após a reprodução
          audioElement.onended = () => document.body.removeChild(audioElement)
          return true
        } catch (fallbackError) {
          console.error(`Falha no método alternativo para ${audioPath}:`, fallbackError)
          if (document.body.contains(audioElement)) {
            document.body.removeChild(audioElement)
          }
          return false
        }
      }
    } catch (error) {
      console.error(`Erro crítico ao reproduzir áudio ${audioPath}:`, error)
      return false
    }
  }

  // Função para anunciar a senha usando síntese de voz
  const anunciarSenha = (senha) => {
    if (!senha) return false
    
    try {
      // Usa a API de síntese de voz do navegador como alternativa aos arquivos de áudio
      if ('speechSynthesis' in window) {
        // Cancela qualquer fala anterior
        window.speechSynthesis.cancel()
        
        // Cria uma nova instância de fala
        const msg = new SpeechSynthesisUtterance()
        
        // Define o texto a ser falado
        const numeroSenha = senha.numero || ''
        const guiche = senha.guiche || ''
        msg.text = `Senha ${numeroSenha}, guichê ${guiche}`
        
        // Define o volume
        msg.volume = (config.volume || 80) / 100
        
        // Define a voz com base na configuração
        const voices = window.speechSynthesis.getVoices()
        if (voices.length > 0) {
          // Tenta encontrar uma voz em português
          const ptVoices = voices.filter(voice => 
            voice.lang.includes('pt') || voice.lang.includes('PT'))
          
          if (ptVoices.length > 0) {
            // Se encontrou vozes em português, usa a primeira
            msg.voice = ptVoices[0]
          }
        }
        
        // Fala o texto
        window.speechSynthesis.speak(msg)
        console.log('Anunciando senha usando síntese de voz:', msg.text)
        
        return true
      } else {
        console.warn('API de síntese de voz não disponível neste navegador')
        return false
      }
    } catch (error) {
      console.error('Erro ao usar síntese de voz:', error)
      return false
    }
  }

  // Função para reproduzir sequência de áudios
  const playAudioSequence = async (senha) => {
    try {
      // Reproduz o efeito sonoro
      if (config.soundEffect && config.soundEffect !== 'none') {
        // Garante que o caminho do arquivo de som esteja correto
        const soundPath = `/assets/${config.soundEffect}.mp3`
        console.log(`Tentando reproduzir efeito sonoro: ${soundPath}`)
        const soundSuccess = await playAudio(soundPath)
        
        if (soundSuccess) {
          // Só espera o delay se o som foi reproduzido com sucesso
          await new Promise(resolve => setTimeout(resolve, 500)) // Delay entre som e voz
        } else {
          console.warn(`Não foi possível reproduzir o efeito sonoro: ${soundPath}`)
        }
      }

      // Tenta usar a síntese de voz para anunciar a senha
      anunciarSenha(senha)
      
      // Reproduz repetições adicionais se configurado
      if (config.repeatInterval && config.repeatInterval > 1) {
        for (let i = 1; i < config.repeatInterval; i++) {
          await new Promise(resolve => setTimeout(resolve, 3000)) // 3 segundos entre repetições
          anunciarSenha(senha)
        }
      }
    } catch (error) {
      console.error('Erro na sequência de áudio:', error)
    }
  }

  // Função para atualizar as senhas
  const atualizarSenhas = () => {
    try {
      // Obter ID do usuário com fallback para 'guest'
      const userId = user?._id || 'guest';
      const STORAGE_KEY_USER = `${STORAGE_KEY_BASE}_${userId}`;
      
      // Verificar primeiro o localStorage com userId
      let todasSenhas = [];
      let senhasSalvas = localStorage.getItem(STORAGE_KEY_USER);
      
      if (senhasSalvas) {
        try {
          todasSenhas = JSON.parse(senhasSalvas);
          console.log(`[PainelSenhas] Senhas carregadas do localStorage com userId (${todasSenhas.length})`);
        } catch (parseError) {
          console.error('[PainelSenhas] Erro ao analisar senhas do localStorage com userId:', parseError);
        }
      }
      
      // Se não encontrar com userId, tentar o formato antigo sem userId
      if (!todasSenhas || todasSenhas.length === 0) {
        senhasSalvas = localStorage.getItem(STORAGE_KEY_BASE);
        if (senhasSalvas) {
          try {
            todasSenhas = JSON.parse(senhasSalvas);
            console.log(`[PainelSenhas] Senhas carregadas do localStorage sem userId (${todasSenhas.length})`);
          } catch (parseError) {
            console.error('[PainelSenhas] Erro ao analisar senhas do localStorage sem userId:', parseError);
          }
        }
      }
      
      // Se ainda não encontrar dados, usar o contexto
      if (!todasSenhas || todasSenhas.length === 0) {
        const senhasChamadas = getSenhasPorStatus('chamada', userId);
        const senhasFinalizadas = getSenhasPorStatus('finalizada', userId);
        const aguardando = getSenhasPorStatus('aguardando', userId);
        
        todasSenhas = [...senhasChamadas, ...senhasFinalizadas, ...aguardando];
        console.log(`[PainelSenhas] Senhas obtidas do contexto (${todasSenhas.length})`);
      }
      
      // Separar por status
      const senhasChamadas = todasSenhas.filter(s => s.status === 'chamada');
      const aguardando = todasSenhas.filter(s => s.status === 'aguardando');
      
      // Atualiza a lista de senhas aguardando
      setSenhasAguardando(aguardando);
      
      // DEBUG: Verificar as senhas chamadas
      console.log('[PainelSenhas] Total de senhas chamadas encontradas:', senhasChamadas.length);
      if (senhasChamadas.length > 0) {
        console.log('[PainelSenhas] Primeira senha chamada:', senhasChamadas[0]);
      }
      
      // Ordenar senhas chamadas pelo horário mais recente
      const senhasChamadasOrdenadas = [...senhasChamadas].sort((a, b) => {
        if (a.horarioChamada && b.horarioChamada) {
          return new Date(b.horarioChamada) - new Date(a.horarioChamada);
        }
        if (a.horarioChamada) return -1;
        if (b.horarioChamada) return 1;
        return 0;
      });
      
      // DEBUG: Verificar as senhas ordenadas
      console.log('[PainelSenhas] Senhas ordenadas:', senhasChamadasOrdenadas.map(s => ({numero: s.numero || s.tipo + s.valor, hora: s.horarioChamada})));
      
      if (senhasChamadasOrdenadas.length > 0) {
        // A senha atual é a primeira da lista ordenada
        const novaSenhaAtual = senhasChamadasOrdenadas[0];
        
        if (novaSenhaAtual) {
          // Garantir que a senha tenha todos os campos necessários
          const guicheValor = novaSenhaAtual.guiche !== undefined && novaSenhaAtual.guiche !== null ? 
            novaSenhaAtual.guiche : 'N/A';
            
          const senhaAtualFormatada = {
            ...novaSenhaAtual,
            id: novaSenhaAtual._id || novaSenhaAtual.id,
            numero: novaSenhaAtual.numero || (novaSenhaAtual.valor ? `${novaSenhaAtual.tipo}${String(novaSenhaAtual.valor).padStart(2, '0')}` : `${novaSenhaAtual.tipo}01`),
            guiche: guicheValor
          };
          
          setSenhaAtual(senhaAtualFormatada);
          
          // Verifica se a senha foi chamada recentemente (últimos 5 segundos)
          const horarioChamada = new Date(novaSenhaAtual.horarioChamada);
          const agora = new Date();
          const diferencaSegundos = (agora - horarioChamada) / 1000;
          
          if (diferencaSegundos < 5) {
            // Reproduzir áudio de chamada
            playAudioSequence(senhaAtualFormatada);
          }
        } else {
          setSenhaAtual(null);
        }
        
        // Atualiza a lista de últimas senhas (excluindo a senha atual)
        // Importante: inclui TODAS as senhas chamadas, não apenas 8
        const ultimasSenhasLista = senhasChamadasOrdenadas.slice(1);
        
        // DEBUG: Verificar as últimas senhas
        console.log('[PainelSenhas] Últimas senhas lista (antes de formatar):', ultimasSenhasLista.length);
        
        // Garantir que as senhas tenham todos os campos necessários
        const senhasFormatadas = ultimasSenhasLista.map(senha => {
          const guicheValor = senha.guiche !== undefined && senha.guiche !== null ? 
            senha.guiche : 'N/A';
            
          return {
            ...senha,
            id: senha._id || senha.id,
            numero: senha.numero || (senha.valor ? `${senha.tipo}${String(senha.valor).padStart(2, '0')}` : `${senha.tipo}01`),
            guiche: guicheValor
          };
        });
        
        // Persistir em localStorage para recuperar após reload
        try {
          localStorage.setItem('ultimasSenhasChamadas_' + userId, JSON.stringify(senhasFormatadas));
        } catch (e) {
          console.warn('[PainelSenhas] Erro ao salvar últimas senhas no localStorage:', e);
        }
        
        setUltimasSenhas(senhasFormatadas);
        console.log('[PainelSenhas] Últimas senhas atualizadas:', senhasFormatadas.length);
      } else {
        // Se não houver senhas chamadas, tentar recuperar do localStorage
        try {
          const ultimasSenhasSalvas = localStorage.getItem('ultimasSenhasChamadas_' + userId);
          if (ultimasSenhasSalvas) {
            const senhasParsed = JSON.parse(ultimasSenhasSalvas);
            if (Array.isArray(senhasParsed) && senhasParsed.length > 0) {
              console.log('[PainelSenhas] Recuperando últimas senhas do localStorage:', senhasParsed.length);
              setUltimasSenhas(senhasParsed);
              return;
            }
          }
        } catch (e) {
          console.warn('[PainelSenhas] Erro ao recuperar últimas senhas do localStorage:', e);
        }
        
        // Se não encontrou nada no localStorage, zera os estados
        setSenhaAtual(null);
        setUltimasSenhas([]);
      }
    } catch (error) {
      console.error('[PainelSenhas] Erro ao atualizar senhas:', error);
    }
  };

  // Atualiza as senhas e o relógio a cada 1 segundo
  useEffect(() => {
    // Função para limpar dados do componente
    const limparDadosComponente = () => {
      console.log('[PainelSenhas] Limpando dados do componente');
      // Limpar estados do componente
      setSenhaAtual(null);
      setUltimasSenhas([]);
      setSenhasAguardando([]);
      
      // Limpar dados do localStorage relacionados às últimas senhas
      try {
        const userId = user?._id || 'guest';
        localStorage.removeItem('ultimasSenhasChamadas');
        localStorage.removeItem(`ultimasSenhasChamadas_${userId}`);
        console.log('[PainelSenhas] Dados de últimas senhas removidos do localStorage');
      } catch (e) {
        console.warn('[PainelSenhas] Erro ao remover dados do localStorage:', e);
      }
    };

    // Verificar se está recarregando após limpeza
    const limpezaCompleta = localStorage.getItem('_limpeza_completa');
    const limpezaSolicitada = localStorage.getItem('_limpeza_solicitada');
    const timestampLimpeza = localStorage.getItem('_timestamp_limpeza');
    
    if (limpezaCompleta || limpezaSolicitada || timestampLimpeza) {
      console.log('[PainelSenhas] Detectada limpeza de dados');
      if (limpezaCompleta) localStorage.removeItem('_limpeza_completa');
      limparDadosComponente();
    }
    
    atualizarSenhas();
    
    const senhasInterval = setInterval(atualizarSenhas, 1000);
    const clockInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    
    // Adiciona listener para atualizações de outras abas ou limpeza
    const handleStorageChange = (e) => {
      // Obter ID do usuário com fallback para 'guest'
      const userId = user?._id || 'guest';
      const STORAGE_KEY_USER = `${STORAGE_KEY_BASE}_${userId}`;
      
      // Detectar mudanças relacionadas a senhas
      if (e.key === STORAGE_KEY_BASE || 
          e.key === STORAGE_KEY_USER ||
          e.key === 'senhas_timestamp' ||
          e.key === `senhas_timestamp_${userId}` ||
          e.key?.includes('senha') ||
          e.key?.includes('sistema') ||
          // Evento especial para limpeza de dados (key é null quando item é removido)
          e.key === null) {
        console.log('[PainelSenhas] Detectada mudança no localStorage:', e.key);
        atualizarSenhas();
      }
      
      // Detectar eventos de limpeza
      if (e.key === '_limpeza_completa' || 
          e.key === '_limpeza_solicitada' || 
          e.key === '_timestamp_limpeza' || 
          e.key === '_estatisticas_limpas') {
        console.log('[PainelSenhas] Detectado evento de limpeza:', e.key, e.newValue);
        if (e.newValue) {
          limparDadosComponente();
        }
      }
      
      // Detectar mudanças na configuração
      if (e.key === 'painelConfig') {
        try {
          if (e.newValue) {
            const newConfig = JSON.parse(e.newValue);
            setConfig(prev => ({...prev, ...newConfig}));
          } else {
            // Se a configuração foi removida, usar configuração padrão
            setConfig({
              title: 'Painel de Senhas',
              theme: 'light',
              backgroundColor: '#f5f5f5',
              textColor: '#333',
              senhaColor: '#3498db',
              fontFamily: 'Arial',
              fontSize: 120,
              logo: null,
              backgroundType: 'color',
              backgroundImage: null,
              footerText: 'Sistema de Gerenciamento de Senhas',
              layoutTheme: 'default',
              senhaAtualBgColor: 'white',
              senhasListBgColor: 'white',
              headerColor: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
              headerTextColor: 'white',
              footerColor: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
              footerTextColor: 'white',
              borderRadius: '12px',
              borderStyle: 'none',
              borderColor: '#ddd',
              borderWidth: '1px',
              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
              hideBorderAccent: false,
              hideUnderline: false,
              hideReloj: false,
              hideFooter: false,
              textShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
              itemBgColor: '#f8f9fa',
              itemBorderRadius: '8px',
              itemBoxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',
              logoPosition: 'center',
              logoSize: 'medium',
              customCss: '',
              tipoColors: {
                P: '#e74c3c',
                N: '#3498db',
                R: '#2ecc71',
                default: '#7f8c8d'
              }
            });
          }
        } catch (error) {
          console.error('[PainelSenhas] Erro ao atualizar configuração:', error);
        }
      }
    };
    
    // Registrar para eventos de storage e eventos personalizados
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      clearInterval(senhasInterval);
      clearInterval(clockInterval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [getSenhasPorStatus, user]); // Adicionar user como dependência
  
  const getTipoDescricao = (tipo) => {
    switch(tipo) {
      case TIPOS_SENHA.PRIORITARIA: return 'Prioritária';
      case TIPOS_SENHA.NORMAL: return 'Normal';
      case TIPOS_SENHA.RAPIDO: return 'Atendimento Rápido';
      default: return '';
    }
  }
  
  const formatTime = (date) => {
    return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
  }
  
  const formatDate = (date) => {
    return date.toLocaleDateString('pt-BR', { 
      weekday: 'long', 
      day: '2-digit', 
      month: 'long', 
      year: 'numeric' 
    })
  }
  
  const calcularTempoEspera = (dataGeracao) => {
    if (!dataGeracao) return '0 min';
    
    try {
      const agora = new Date();
      const geracao = new Date(dataGeracao);
      
      if (isNaN(geracao.getTime())) return '0 min';
      
      const diferencaMs = agora - geracao;
      const minutos = Math.floor(diferencaMs / (1000 * 60));
      
      if (minutos < 60) {
        return `${minutos} min`;
      } else {
        const horas = Math.floor(minutos / 60);
        const minutosRestantes = minutos % 60;
        return `${horas}h ${minutosRestantes}min`;
      }
    } catch (error) {
      console.error('Erro ao calcular tempo de espera:', error);
      return '0 min';
    }
  }
  
  return (
    <Container 
      $backgroundColor={config.backgroundType === 'color' ? config.backgroundColor : 'transparent'}
      $backgroundImage={config.backgroundType === 'image' ? config.backgroundImage : null}
      $fontFamily={config.fontFamily}
      $layoutTheme={config.layoutTheme}
      $backgroundType={config.backgroundType}
      $customCss={config.customCss}
      $textColor={config.textColor}
    >
      <Header 
        $layoutTheme={config.layoutTheme}
        $headerColor={config.headerColor}
        $headerTextColor={config.headerTextColor}
      >
        {config.logo && (
          <img 
            src={config.logo} 
            alt="Logo" 
            style={{ 
              maxHeight: '40px', 
              marginRight: '15px',
              position: config.logoPosition || 'center',
              maxWidth: config.logoSize === 'small' ? '80px' : 
                       config.logoSize === 'large' ? '200px' : '120px'
            }} 
          />
        )}
        <Title>{config.title}</Title>
      </Header>
      
      <PainelContainer $layoutTheme={config.layoutTheme}>
        <SenhaAtualContainer 
          $layoutTheme={config.layoutTheme}
          $senhaAtualBgColor={config.senhaAtualBgColor}
          $borderRadius={config.borderRadius}
          $boxShadow={config.boxShadow}
          $borderStyle={config.borderStyle}
          $borderColor={config.borderColor}
          $borderWidth={config.borderWidth}
        >
          <SenhaAtualTitle 
            $layoutTheme={config.layoutTheme}
            $textColor={config.textColor}
            $fontFamily={config.fontFamily}
            $fontSize={config.fontSize}
          >
            Senha Atual
          </SenhaAtualTitle>
          
          {senhaAtual ? (
            <>
              <SenhaAtualTipo $textColor={config.textColor}>
                {getTipoDescricao(senhaAtual.tipo)}
              </SenhaAtualTipo>
              
              <SenhaAtualNumero 
                $tipo={senhaAtual.tipo}
                $senhaColor={config.senhaColor}
                $fontSize={config.fontSize}
                $fontFamily={config.fontFamily}
                $textShadow={config.textShadow}
                $layoutTheme={config.layoutTheme}
                $tipoColors={config.tipoColors}
              >
                {senhaAtual.numero}
              </SenhaAtualNumero>
              
              <GuicheInfo $textColor={config.textColor}>
                Guichê {senhaAtual.guiche}
              </GuicheInfo>
            </>
          ) : (
            <SenhaAtualTipo $textColor={config.textColor}>Aguardando chamada...</SenhaAtualTipo>
          )}
        </SenhaAtualContainer>
        
        <UltimasSenhasContainer 
          $layoutTheme={config.layoutTheme}
          $senhasListBgColor={config.senhasListBgColor}
          $borderRadius={config.borderRadius}
          $boxShadow={config.boxShadow}
          $borderStyle={config.borderStyle}
          $borderColor={config.borderColor}
          $borderWidth={config.borderWidth}
        >
          <UltimasSenhasTitle 
            $layoutTheme={config.layoutTheme}
            $textColor={config.textColor}
            $fontFamily={config.fontFamily}
          >
            Últimas Senhas Chamadas
          </UltimasSenhasTitle>
          
          <SenhasList>
            {ultimasSenhas.length > 0 ? (
              ultimasSenhas.map(senha => (
                <SenhaItem 
                  key={senha._id || senha.id} 
                  $tipo={senha.tipo}
                  $itemBgColor={config.itemBgColor}
                  $itemBorderRadius={config.itemBorderRadius}
                  $itemBoxShadow={config.itemBoxShadow}
                  $tipoColors={config.tipoColors}
                  $layoutTheme={config.layoutTheme}
                >
                  <SenhaNumero 
                    $tipo={senha.tipo}
                    $tipoColors={config.tipoColors}
                    $fontFamily={config.fontFamily}
                    $layoutTheme={config.layoutTheme}
                  >
                    {senha.numero}
                  </SenhaNumero>
                  <SenhaGuiche>
                    Guichê {senha.guiche || 'N/A'}
                  </SenhaGuiche>
                </SenhaItem>
              ))
            ) : (
              <p style={{ textAlign: 'center' }}>Nenhuma senha chamada recentemente.</p>
            )}
          </SenhasList>
        </UltimasSenhasContainer>
        
        <SenhasAguardandoContainer 
          $layoutTheme={config.layoutTheme}
          $senhasListBgColor={config.senhasListBgColor}
          $borderRadius={config.borderRadius}
          $boxShadow={config.boxShadow}
          $borderStyle={config.borderStyle}
          $borderColor={config.borderColor}
          $borderWidth={config.borderWidth}
        >
          <UltimasSenhasTitle 
            $layoutTheme={config.layoutTheme}
            $textColor={config.textColor}
            $fontFamily={config.fontFamily}
          >
            Senhas Aguardando
          </UltimasSenhasTitle>
          
          <SenhasList>
            {senhasAguardando.length > 0 ? (
              senhasAguardando
                .sort((a, b) => {
                  // Ordena por tipo (prioritária primeiro) e depois por horário de geração
                  if (a.tipo === TIPOS_SENHA.PRIORITARIA && b.tipo !== TIPOS_SENHA.PRIORITARIA) return -1;
                  if (a.tipo !== TIPOS_SENHA.PRIORITARIA && b.tipo === TIPOS_SENHA.PRIORITARIA) return 1;
                  return new Date(a.horarioGeracao) - new Date(b.horarioGeracao);
                })
                .map(senha => (
                <SenhaItem 
                  key={senha._id || senha.id} 
                  $tipo={senha.tipo}
                  $itemBgColor={config.itemBgColor}
                  $itemBorderRadius={config.itemBorderRadius}
                  $itemBoxShadow={config.itemBoxShadow}
                  $tipoColors={config.tipoColors}
                  $layoutTheme={config.layoutTheme}
                >
                  <SenhaNumero 
                    $tipo={senha.tipo}
                    $tipoColors={config.tipoColors}
                    $fontFamily={config.fontFamily}
                    $layoutTheme={config.layoutTheme}
                  >
                    {senha.numero}
                  </SenhaNumero>
                  <SenhaGuiche>
                    Espera: {calcularTempoEspera(senha.horarioGeracao)}
                  </SenhaGuiche>
                </SenhaItem>
              ))
            ) : (
              <p style={{ textAlign: 'center' }}>Nenhuma senha aguardando atendimento.</p>
            )}
          </SenhasList>
        </SenhasAguardandoContainer>
      </PainelContainer>
      
      <Footer 
        $layoutTheme={config.layoutTheme}
        $footerColor={config.footerColor}
        $footerTextColor={config.footerTextColor}
        $hideBorderAccent={config.hideBorderAccent}
        style={{
          display: config.hideFooter ? 'none' : 'block'
        }}
      >
        {!config.hideReloj && (
          <Clock>
            {formatTime(currentTime)} - {formatDate(currentTime)}
          </Clock>
        )}
        {config.footerText && (
          <div style={{ marginTop: '5px' }}>{config.footerText}</div>
        )}
      </Footer>
    </Container>
  )
}

export default PainelSenhas