/**
 * Definições de temas e layouts para o painel de senhas
 * Este arquivo contém as configurações para diferentes layouts que podem ser aplicados ao painel
 * Expandido para suportar personalização avançada de componentes individuais
 */

const layoutThemes = {
  // Layout padrão (atual)
  padrao: {
    id: 'padrao',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Layout padrão do sistema com visualização equilibrada',
    colors: { 
      bg: '#f8f9fa', 
      text: '#2c3e50', 
      senha: '#3498db',
      header: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
      headerText: '#ffffff',
      // Cores individuais para cada componente
      senhaAtual: {
        bg: '#ffffff',
        text: '#2c3e50',
        numero: '#3498db',
        tipo: '#7f8c8d',
        guiche: '#2c3e50'
      },
      ultimasSenhas: {
        bg: '#ffffff',
        text: '#2c3e50',
        titulo: '#2c3e50',
        item: '#f8f9fa'
      },
      senhasAguardando: {
        bg: '#ffffff',
        text: '#2c3e50',
        titulo: '#2c3e50',
        item: '#f8f9fa'
      },
      footer: {
        bg: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
        text: '#ffffff'
      }
    },
    layout: {
      painelContainer: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '20px'
      },
      senhaAtualContainer: {
        flex: 2,
        minWidth: '300px',
        // Propriedades para personalizar bordas, sombras e cantos arredondados
        borderRadius: '12px',
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
        border: 'none',
        padding: '25px'
      },
      senhasListContainer: {
        flex: 1,
        minWidth: '300px',
        // Propriedades para personalizar bordas, sombras e cantos arredondados
        borderRadius: '12px',
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
        border: 'none',
        padding: '25px'
      }
    },
    // Configurações de fonte para diferentes elementos
    fonts: {
      senhaAtual: {
        size: 120,
        family: 'Arial'
      },
      senhaGuiche: {
        size: 48,
        family: 'Arial'
      },
      senhaLista: {
        size: 24,
        family: 'Arial'
      },
      titulo: {
        size: 32,
        family: 'Arial'
      },
      footer: {
        size: 18,
        family: 'Arial'
      }
    },
    // Opções de posicionamento e tamanho do logo
    logo: {
      position: 'center', // 'left', 'center', 'right'
      size: 'medium', // 'small', 'medium', 'large'
      margin: '10px'
    }
  },
  
  // Layout minimalista
  minimalista: {
    id: 'minimalista',
    name: 'Minimalista',
    description: 'Layout simplificado com foco na senha atual',
    colors: { 
      bg: '#ffffff', 
      text: '#333333', 
      senha: '#555555',
      header: '#f5f5f5',
      headerText: '#333333',
      // Cores individuais para cada componente
      senhaAtual: {
        bg: '#ffffff',
        text: '#333333',
        numero: '#555555',
        tipo: '#777777',
        guiche: '#333333'
      },
      ultimasSenhas: {
        bg: '#f9f9f9',
        text: '#333333',
        titulo: '#333333',
        item: '#f5f5f5'
      },
      senhasAguardando: {
        bg: '#f9f9f9',
        text: '#333333',
        titulo: '#333333',
        item: '#f5f5f5'
      },
      footer: {
        bg: '#f5f5f5',
        text: '#333333'
      }
    },
    layout: {
      painelContainer: {
        display: 'grid',
        gridTemplateColumns: '1fr',
        gridTemplateRows: 'auto auto',
        gap: '15px'
      },
      senhaAtualContainer: {
        gridColumn: '1',
        gridRow: '1',
        padding: '40px 20px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        border: '1px solid #eee',
        padding: '40px 20px'
      },
      senhasListContainer: {
        gridColumn: '1',
        gridRow: '2',
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '15px',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        border: '1px solid #eee',
        padding: '20px'
      }
    },
    // Configurações de fonte para diferentes elementos
    fonts: {
      senhaAtual: {
        size: 100,
        family: 'Helvetica'
      },
      senhaGuiche: {
        size: 36,
        family: 'Helvetica'
      },
      senhaLista: {
        size: 20,
        family: 'Helvetica'
      },
      titulo: {
        size: 28,
        family: 'Helvetica'
      },
      footer: {
        size: 16,
        family: 'Helvetica'
      }
    },
    // Opções de posicionamento e tamanho do logo
    logo: {
      position: 'left',
      size: 'small',
      margin: '8px'
    }
  },
  
  // Layout de alto contraste
  altoContraste: {
    id: 'altoContraste',
    name: 'Alto Contraste',
    description: 'Layout com alto contraste para melhor visibilidade',
    colors: { 
      bg: '#000000', 
      text: '#ffffff', 
      senha: '#ffff00',
      header: '#000000',
      headerText: '#ffffff'
    },
    layout: {
      painelContainer: {
        display: 'flex',
        flexDirection: 'column',
        gap: '20px'
      },
      senhaAtualContainer: {
        padding: '30px',
        border: '3px solid #ffffff'
      },
      senhasListContainer: {
        display: 'flex',
        gap: '20px'
      }
    }
  },
  
  // Layout para telas grandes
  telasGrandes: {
    id: 'telasGrandes',
    name: 'Telas Grandes',
    description: 'Layout otimizado para monitores e TVs de grande formato',
    colors: { 
      bg: '#0a192f', 
      text: '#e6f1ff', 
      senha: '#64ffda',
      header: '#172a45',
      headerText: '#e6f1ff'
    },
    layout: {
      painelContainer: {
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gridTemplateRows: 'auto auto',
        gap: '25px',
        padding: '30px'
      },
      senhaAtualContainer: {
        gridColumn: '1',
        gridRow: '1 / span 2',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '50px 20px'
      },
      senhasListContainer: {
        gridColumn: '2',
        gridRow: '1 / span 2'
      }
    }
  },
  
  // Layout horizontal
  horizontal: {
    id: 'horizontal',
    name: 'Horizontal',
    description: 'Layout com disposição horizontal dos elementos',
    colors: { 
      bg: '#f0f4f8', 
      text: '#334e68', 
      senha: '#0f609b',
      header: '#334e68',
      headerText: '#f0f4f8'
    },
    layout: {
      painelContainer: {
        display: 'flex',
        flexDirection: 'row',
        gap: '20px',
        height: 'calc(100vh - 140px)'
      },
      senhaAtualContainer: {
        flex: '0 0 50%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center'
      },
      senhasListContainer: {
        flex: '0 0 50%',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px'
      }
    }
  }
};

export default layoutThemes;