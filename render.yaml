services:
  - type: web
    name: chamador-backend
    env: node
    plan: free
    buildCommand: cd backend && npm install
    startCommand: cd backend && node server.js
    envVars:
      - key: NODE_ENV
        value: production
      - key: JWT_SECRET
        value: chamadorSenhasSecretKey2024
      - key: MONGODB_URI
        value: mongodb+srv://sandrod:<EMAIL>/chamadorSenhas?retryWrites=true&w=majority
      - key: CORS_ALLOWED_ORIGINS
        value: https://chamador-git-main-sandro-denis-projects.vercel.app,https://chamador.vercel.app,http://localhost:3000,http://localhost:5173,http://localhost:3001
      - key: PORT
        value: 10000
    healthCheckPath: /api/check-connection
