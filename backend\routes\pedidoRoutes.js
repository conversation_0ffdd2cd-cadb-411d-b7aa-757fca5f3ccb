import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import jwt from 'jsonwebtoken';

const router = express.Router();

// Middleware para verificar token JWT (apenas para rotas protegidas)
const verificarToken = (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'Token não fornecido' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    console.error('Erro ao verificar token:', error);
    res.status(401).json({ message: 'Token inválido' });
  }
};

// Estados dos pedidos
const STATUS_PEDIDO = {
  AGUARDANDO: 'aguardando',
  EM_ANDAMENTO: 'em_andamento',
  PRONTO: 'pronto',
  ENTREGUE: 'entregue',
  CANCELADO: 'cancelado'
};

// Gerar ID único para pedido
const gerarIdPedido = () => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `PED${timestamp}${random}`;
};

// ==================== ROTAS PÚBLICAS ====================

// Criar novo pedido (público - não precisa de autenticação)
router.post('/', async (req, res) => {
  try {
    console.log('Criando novo pedido:', req.body);

    const {
      nomeCliente,
      telefone,
      email,
      itens,
      observacoes,
      empresaId
    } = req.body;

    // Validações
    if (!nomeCliente || !telefone || !itens || !empresaId) {
      return res.status(400).json({
        message: 'Nome, telefone, itens e empresaId são obrigatórios'
      });
    }

    const db = req.app.locals.db;
    const pedidos = db.collection('pedidos');

    const novoPedido = {
      _id: uuidv4(),
      id: gerarIdPedido(), // ID legível para o cliente
      nomeCliente,
      telefone,
      email: email || '',
      itens,
      observacoes: observacoes || '',
      empresaId,
      status: STATUS_PEDIDO.AGUARDANDO,
      feedback: '',
      dataCriacao: new Date().toISOString(),
      dataAtualizacao: new Date().toISOString()
    };

    await pedidos.insertOne(novoPedido);
    console.log('Pedido criado com sucesso:', novoPedido.id);

    res.status(201).json(novoPedido);
  } catch (error) {
    console.error('Erro ao criar pedido:', error);
    res.status(500).json({
      message: 'Erro ao criar pedido',
      error: error.message
    });
  }
});

// Buscar pedido por ID (público - para acompanhamento)
router.get('/:pedidoId', async (req, res) => {
  try {
    const { pedidoId } = req.params;
    console.log('Buscando pedido com ID:', pedidoId);

    const db = req.app.locals.db;
    const pedidos = db.collection('pedidos');

    // Buscar por ID legível ou _id do MongoDB
    const pedido = await pedidos.findOne({
      $or: [
        { id: pedidoId },
        { _id: pedidoId }
      ]
    });

    if (!pedido) {
      console.log('Pedido não encontrado:', pedidoId);
      return res.status(404).json({
        message: 'Pedido não encontrado'
      });
    }

    console.log('Pedido encontrado:', pedido.id);
    res.json(pedido);
  } catch (error) {
    console.error('Erro ao buscar pedido:', error);
    res.status(500).json({
      message: 'Erro ao buscar pedido',
      error: error.message
    });
  }
});

// ==================== ROTAS PROTEGIDAS ====================

// Buscar todos os pedidos de uma empresa (protegido)
router.get('/', verificarToken, async (req, res) => {
  try {
    const { empresaId } = req.query;
    const userId = req.userId;

    console.log('Buscando pedidos para empresa:', empresaId || userId);

    const db = req.app.locals.db;
    const pedidos = db.collection('pedidos');

    // Usar empresaId da query ou userId do token
    const filtroEmpresa = empresaId || userId;

    const pedidosDaEmpresa = await pedidos.find({
      empresaId: filtroEmpresa
    }).sort({ dataCriacao: -1 }).toArray();

    console.log(`Encontrados ${pedidosDaEmpresa.length} pedidos para empresa ${filtroEmpresa}`);
    res.json(pedidosDaEmpresa);
  } catch (error) {
    console.error('Erro ao buscar pedidos:', error);
    res.status(500).json({
      message: 'Erro ao buscar pedidos',
      error: error.message
    });
  }
});

// Atualizar status do pedido (protegido)
router.put('/:pedidoId', verificarToken, async (req, res) => {
  try {
    const { pedidoId } = req.params;
    const { status, feedback } = req.body;

    console.log(`Atualizando pedido ${pedidoId} para status: ${status}`);

    // Validar status
    if (!Object.values(STATUS_PEDIDO).includes(status)) {
      return res.status(400).json({
        message: 'Status inválido'
      });
    }

    const db = req.app.locals.db;
    const pedidos = db.collection('pedidos');

    const updateData = {
      status,
      dataAtualizacao: new Date().toISOString()
    };

    // Adicionar feedback se fornecido
    if (feedback !== undefined) {
      updateData.feedback = feedback;
    }

    const resultado = await pedidos.updateOne(
      {
        $or: [
          { id: pedidoId },
          { _id: pedidoId }
        ]
      },
      { $set: updateData }
    );

    if (resultado.matchedCount === 0) {
      return res.status(404).json({
        message: 'Pedido não encontrado'
      });
    }

    console.log(`Pedido ${pedidoId} atualizado com sucesso`);

    // Buscar e retornar o pedido atualizado
    const pedidoAtualizado = await pedidos.findOne({
      $or: [
        { id: pedidoId },
        { _id: pedidoId }
      ]
    });

    res.json(pedidoAtualizado);
  } catch (error) {
    console.error('Erro ao atualizar pedido:', error);
    res.status(500).json({
      message: 'Erro ao atualizar pedido',
      error: error.message
    });
  }
});

// Deletar pedido (protegido)
router.delete('/:pedidoId', verificarToken, async (req, res) => {
  try {
    const { pedidoId } = req.params;
    console.log('Deletando pedido:', pedidoId);

    const db = req.app.locals.db;
    const pedidos = db.collection('pedidos');

    const resultado = await pedidos.deleteOne({
      $or: [
        { id: pedidoId },
        { _id: pedidoId }
      ]
    });

    if (resultado.deletedCount === 0) {
      return res.status(404).json({
        message: 'Pedido não encontrado'
      });
    }

    console.log(`Pedido ${pedidoId} deletado com sucesso`);
    res.json({ message: 'Pedido deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar pedido:', error);
    res.status(500).json({
      message: 'Erro ao deletar pedido',
      error: error.message
    });
  }
});

// Buscar estatísticas de pedidos (protegido)
router.get('/stats/resumo', verificarToken, async (req, res) => {
  try {
    const { empresaId } = req.query;
    const userId = req.userId;

    const filtroEmpresa = empresaId || userId;
    console.log('Buscando estatísticas para empresa:', filtroEmpresa);

    const db = req.app.locals.db;
    const pedidos = db.collection('pedidos');

    // Buscar todos os pedidos da empresa
    const todosPedidos = await pedidos.find({
      empresaId: filtroEmpresa
    }).toArray();

    // Calcular estatísticas
    const stats = {
      total: todosPedidos.length,
      aguardando: todosPedidos.filter(p => p.status === STATUS_PEDIDO.AGUARDANDO).length,
      emAndamento: todosPedidos.filter(p => p.status === STATUS_PEDIDO.EM_ANDAMENTO).length,
      prontos: todosPedidos.filter(p => p.status === STATUS_PEDIDO.PRONTO).length,
      entregues: todosPedidos.filter(p => p.status === STATUS_PEDIDO.ENTREGUE).length,
      cancelados: todosPedidos.filter(p => p.status === STATUS_PEDIDO.CANCELADO).length
    };

    console.log('Estatísticas calculadas:', stats);
    res.json(stats);
  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({
      message: 'Erro ao buscar estatísticas',
      error: error.message
    });
  }
});

export default router;
