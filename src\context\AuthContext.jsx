import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  register as registerUser,
  login as loginUser,
  logout as logoutUser,
  isAuthenticated as checkAuth,
  getCurrentUser,
  updateUserConfig
} from '../config/auth';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkUser = async () => {
      try {
        console.log('[AuthContext] Verificando autenticação...')
        if (checkAuth()) {
          console.log('[AuthContext] Token encontrado, obtendo dados do usuário...')

          // Tentar obter dados do usuário com retry
          let userData = null;
          let tentativas = 0;
          const maxTentativas = 3;

          while (!userData && tentativas < maxTentativas) {
            tentativas++;
            console.log(`[AuthContext] Tentativa ${tentativas} de ${maxTentativas}...`);

            try {
              userData = await getCurrentUser();
              if (userData) {
                console.log('[AuthContext] Usuário autenticado:', userData.email)
                setUser(userData);
                break;
              }
            } catch (userError) {
              console.warn(`[AuthContext] Erro na tentativa ${tentativas}:`, userError.message);

              // Se for erro de rede ou temporário, aguardar antes de tentar novamente
              if (tentativas < maxTentativas) {
                await new Promise(resolve => setTimeout(resolve, 1000 * tentativas));
              }
            }
          }

          // Só fazer logout se todas as tentativas falharam E o token for inválido
          if (!userData) {
            console.log('[AuthContext] Não foi possível obter dados do usuário após várias tentativas')
            // Verificar se o token ainda é válido antes de fazer logout
            const token = localStorage.getItem('token');
            if (!token) {
              console.log('[AuthContext] Token não encontrado, fazendo logout...')
              await logout();
            } else {
              console.log('[AuthContext] Token existe, criando usuário básico para manter sessão')
              // Criar usuário básico com dados do localStorage para manter sessão ativa
              const userId = localStorage.getItem('userId') || 'temp_user';
              const userBasico = {
                _id: userId,
                id: userId, // Compatibilidade
                companyId: userId, // Usar o próprio userId como companyId para pedidos
                email: localStorage.getItem('userEmail') || '<EMAIL>',
                companyName: localStorage.getItem('companyName') || 'Empresa',
                isTemporary: true // Flag para indicar que são dados temporários
              };
              setUser(userBasico);
              console.log('[AuthContext] 🔧 Usuário básico criado para manter sessão ativa:', {
                email: userBasico.email,
                _id: userBasico._id,
                companyId: userBasico.companyId,
                isTemporary: userBasico.isTemporary
              });
            }
          }
        } else {
          console.log('[AuthContext] Nenhum token encontrado')
        }
      } catch (error) {
        console.error('[AuthContext] Erro ao verificar usuário:', error);
        setError(error.message);

        // Só fazer logout se for erro de autenticação, não erro de rede
        if (error.message.includes('401') || error.message.includes('Token inválido')) {
          console.log('[AuthContext] Erro de autenticação, fazendo logout...')
          await logout();
        } else {
          console.log('[AuthContext] Erro temporário, mantendo sessão...')
        }
      } finally {
        setLoading(false);
      }
    };

    checkUser();
  }, []);

  const register = async (email, password, companyName) => {
    try {
      setLoading(true);
      setError(null);
      const { user: newUser } = await registerUser(email, password, companyName);
      setUser(newUser);
      return newUser;
    } catch (error) {
      console.error('Erro no registro:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      const { user: loggedUser } = await loginUser(email, password);
      setUser(loggedUser);
      return loggedUser;
    } catch (error) {
      console.error('Erro no login:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      await logoutUser();
      setUser(null);
    } catch (error) {
      console.error('Erro no logout:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = async (config) => {
    try {
      setLoading(true);
      setError(null);
      const updatedUser = await updateUserConfig(config);
      setUser(updatedUser);
      return updatedUser;
    } catch (error) {
      console.error('Erro ao atualizar configurações:', error);
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        error,
        register,
        login,
        logout,
        updateConfig,
        isAuthenticated: !!user
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};