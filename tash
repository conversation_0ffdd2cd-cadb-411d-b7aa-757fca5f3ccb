[33mb788ac5[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m, [m[1;31morigin/main[m[33m)[m atualização do painel de exibição de senhas e opcoes de layoult
[33m0ddf393[m Removido opção de registro de novos usuários da página de login
[33m1742401[m Implementação do painel de administração com funcionalidades de gerenciamento de usuários
[33m7c5658e[m Atualizações no servidor
[33mc5a788d[m Atualização dos arquivos ConfiguracaoLayout.jsx e GerarSenha.jsx com melhorias na interface e funcionalidades
[33mee89bd0[m Implementação de configurações de usuário persistentes e melhorias no gerador de senhas
[33m0175b23[m Fix: corrigido problema de últimas senhas não aparecendo no display
[33m3382e5e[m Fix: corrigido persistência de dados nas estatísticas após limpeza com melhorias nos flags de limpeza
[33m251761e[m Fix: corrigido problema de persistência de dados nas estatísticas após limpeza
[33m01f2ae5[m Corrigido problema de persistência de dados nas estatísticas e display após limpeza de dados. Implementado sistema de detecção de limpeza com recarregamento automático de página.
[33mf6ac73f[m chore: Atualizacao forcada para reimplantacao nos servidores
[33m61eb5ae[m Melhoria no tratamento de erros para QR code e busca de senhas
[33m7e71a8e[m Forçar nova implantação no Vercel
[33mbd49670[m Melhora tratamento de erros na busca de senha por QR code
[33m085af00[m Aprimorada função de limpeza de dados com melhor tratamento de erros e limpeza mais abrangente
[33md3abc08[m fix: Melhoria completa na limpeza de dados para estatísticas e painel de senhas
[33m681ae8d[m fix: Melhora a limpeza de dados para incluir todas as configurações de layout, estatísticas e display
[33m2ef8d53[m fix: Aprimorada a limpeza de dados para incluir estatísticas e configurações do painel
[33m3e32500[m fix: Implementa isolamento de dados entre usuários usando userId
[33meb9410d[m refactor: Remove a seção de ferramentas de diagnóstico da interface
[33m4104503[m refactor: Simplifica interface removendo botões duplicados de limpeza
[33mec2a001[m fix: Implementa rotas de limpeza simplificadas e melhora fluxo de emergência
[33m8e945f4[m fix: Adiciona ferramentas de diagnóstico para problemas de autenticação e CORS
[33m79799cc[m fix: Melhora configuração CORS e adiciona solução no-cors - Configura CORS_ALLOWED_ORIGINS no servidor, adiciona novas rotas, documentação e função no-cors para evitar bloqueios
[33m30b76cc[m Corrige função de limpeza de dados removendo flag corsError e ajustando mensagens de retorno
[33m4cb923d[m Corrigir problema de CORS na limpeza de dados do servidor
[33m1396ee6[m Atualização do servidor backend e configuração de autenticação
[33m83072d3[m Atualização das funções de autenticação e limpeza de dados
[33m504d2ff[m Mantido apenas o botão de limpeza de dados diretamente no servidor, removidos os outros botões e funções relacionadas
[33maf3b851[m Atualização da página de configuração de layout
[33m2e35900[m Atualização na página de configuração de layout
[33m48b4a12[m Atualização do .env.production e vercel.json para configuração de produção
[33m7a667b7[m Atualização do vercel.json e backend/server.js
[33m6ec7d3f[m Atualizando configurações de API e CORS
[33m544ad88[m fix: Melhora configuração do vercel.json - Corrige rotas 404, adiciona /api/logout, otimiza CORS e cache, configura região GRU1
[33m7e3234e[m fix: Ajusta rotas da API no vercel.json para resolver erro 404
[33ma29bd02[m fix: Remove routes do vercel.json para evitar conflito com rewrites
[33mb2f8c9a[m fix: Ajusta configurações para deploy no Vercel
[33m71fdc47[m Implementação da função limparDadosNoServidorDireto e melhorias no sistema de autenticação
[33m901819c[m fix: implementa rota direta no servidor e super limpeza para resolver erro 500
[33m91d8211[m Melhorias no sistema de limpeza de dados e tratamento de erros
[33md8f32d7[m feat: adiciona metodo de emergencia para limpeza de dados via API alternativa
[33m35d16d6[m feat: melhora diagnóstico de erros na limpeza de dados com servidor alternativo via fetch
[33mf4a3115[m Atualizações no backend: modificações nas rotas e no servidor
[33m2e63fa5[m Atualização do middleware de limpeza de dados com melhor tratamento de erros e logs detalhados
[33m49f5390[m Melhoria: Implementado tratamento detalhado de erros e logs no sistema de limpeza de dados
[33m58959a2[m fix: adiciona acesso direto ao servidor para limpar dados e contornar erro 500
[33m105ac31[m fix: corrige erro 500 ao limpar dados com solução CORS Vercel e adição modo offline
[33m322db2a[m fix: corrige erro 500 ao limpar dados adicionando função robusta de limpeza
[33mbda3bf5[m feat: adiciona endpoint público para buscar senhas aguardando e melhora página de acompanhamento QR code
[33md5c73be[m fix: corrige contagem de senhas na frente na página de acompanhamento QR code
[33m3c80c55[m fix: corrige atualização em tempo real na página de acompanhamento de senha via QR code
[33m8060d5f[m feat: implementa atualização em tempo real na página de acompanhamento QR Code
[33m52073ce[m fix: implementa busca por ID pública para QR codes
[33m4260d96[m Melhoria no componente AcompanharSenha para melhor tratamento de senhas via QR code
[33m5f92633[m Atualização do servidor backend e ajustes na interface do App
[33md9a5cda[m Atualização da página de acompanhamento de senha
[33mf3472ae[m Atualização da página de acompanhamento de senhas e dependências do projeto
[33md8f0c9d[m Atualização do servidor backend e da página de geração de senhas
[33m1627306[m Atualizando configurações do Vite
[33m2a67c1a[m Atualização de dependências e configuração do Vite para resolver problemas de build com qrcode.react
[33m34c4635[m Implementação do QR Code para acompanhamento de senhas
[33ma7874d2[m Corrigido problema de isolamento de dados entre contas de usuários no SenhaContext
[33m7190939[m Melhoria no isolamento de dados entre contas de usuários no SenhaContext
[33medea507[m Melhorias no sistema de áudio e anúncio de senhas
[33m24309ab[m feat: adiciona temas e atualiza páginas
[33mc2be766[m fix: corrige problemas de CORS removendo withCredentials
[33mfc7b02c[m fix: corrige erro 'o is not a function' na geração de senhas
[33mf1e475b[m fix: corrige erro ao gerar senha
[33md422176[m fix: corrige uso de axios no arquivo de autenticação
[33me1b7844[m fix: melhora configuração do proxy e tratamento de erros na API
[33mb85ca69[m feat: implementa separação de senhas por usuário/empresa
[33m586cf26[m fix: corrige URL do backend no proxy do Vercel e na configuração da API
[33m7aabbe9[m feat: implementa proxy via Vercel para evitar problemas de CORS
[33m704b9db[m fix: simplifica configuração CORS e remove headers desnecessários
[33m759004d[m feat: adiciona headers CORS no frontend
[33m33d1aea[m feat: implementa middleware CORS universal
[33m66f2b68[m feat: adiciona logs e configurações CORS no axios
[33m39c3f43[m fix: atualiza configuração CORS para permitir domínio do Vercel
[33m09ace00[m fix: corrige URLs de API e problema de build no Vercel
[33m4d13f39[m fix: ajusta configurações CORS e axios para produção
[33mb824410[m fix: corrige configuração CORS e axios
[33mb9faaa4[m fix: adiciona axios e atualiza configuração do Vite
[33m00713b9[m chore: adiciona configurações para deploy no Vercel
[33m97864ac[m fix: atualiza string de conexão do MongoDB
[33m7c3a56a[m fix: adiciona fallback para MONGODB_URI e melhora logs
[33mb1055c1[m fix: melhorias na conexão MongoDB e logs detalhados
[33me4b21e7[m Initial commit
