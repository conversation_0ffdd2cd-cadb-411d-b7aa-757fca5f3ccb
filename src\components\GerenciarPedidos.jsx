import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { usePedido } from '../context/PedidoContext';

const Container = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 25px;
  margin-bottom: 20px;
`;

const Title = styled.h2`
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const TabContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
`;

const Tab = styled.button`
  background: ${props => props.active ? '#3498db' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};
  border: none;
  padding: 12px 20px;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: ${props => props.active ? '#2980b9' : '#f8f9fa'};
  }
  
  ${props => props.count > 0 && `
    &::after {
      content: '${props.count}';
      position: absolute;
      top: -5px;
      right: -5px;
      background: #e74c3c;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  `}
`;

const PedidosList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
`;

const PedidoCard = styled.div`
  border: 2px solid ${props => {
    switch(props.status) {
      case 'aguardando': return '#ffc107';
      case 'em_andamento': return '#17a2b8';
      case 'pronto': return '#28a745';
      default: return '#e0e0e0';
    }
  }};
  border-radius: 12px;
  padding: 20px;
  background: ${props => {
    switch(props.status) {
      case 'aguardando': return '#fff3cd';
      case 'em_andamento': return '#d1ecf1';
      case 'pronto': return '#d4edda';
      default: return '#f8f9fa';
    }
  }};
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const PedidoHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
`;

const PedidoId = styled.h3`
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
`;

const PedidoTime = styled.span`
  color: #666;
  font-size: 14px;
`;

const PedidoInfo = styled.div`
  margin-bottom: 15px;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
`;

const InfoLabel = styled.span`
  font-weight: 600;
  color: #2c3e50;
`;

const InfoValue = styled.span`
  color: #666;
`;

const PedidoItens = styled.div`
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  border-left: 4px solid #3498db;
`;

const ItensTitle = styled.h4`
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
`;

const ItensText = styled.p`
  margin: 0;
  color: #666;
  line-height: 1.5;
`;

const ActionsContainer = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const ActionButton = styled.button`
  background: ${props => {
    switch(props.variant) {
      case 'start': return 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)';
      case 'ready': return 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)';
      case 'deliver': return 'linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%)';
      case 'cancel': return 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
      default: return 'linear-gradient(135deg, #6c757d 0%, #5a6268 100%)';
    }
  }};
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const FeedbackContainer = styled.div`
  margin-top: 15px;
`;

const FeedbackInput = styled.textarea`
  width: 100%;
  padding: 10px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: #3498db;
  }
`;

const FeedbackActions = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 10px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
`;

const GerenciarPedidos = () => {
  const { 
    pedidos, 
    obterPedidosPorStatus, 
    atualizarStatusPedido, 
    STATUS_PEDIDO,
    loading 
  } = usePedido();
  
  const [activeTab, setActiveTab] = useState(STATUS_PEDIDO.AGUARDANDO);
  const [feedbackInputs, setFeedbackInputs] = useState({});
  const [showFeedback, setShowFeedback] = useState({});

  const tabs = [
    { key: STATUS_PEDIDO.AGUARDANDO, label: 'Aguardando', icon: '⏳' },
    { key: STATUS_PEDIDO.EM_ANDAMENTO, label: 'Em Andamento', icon: '👨‍🍳' },
    { key: STATUS_PEDIDO.PRONTO, label: 'Prontos', icon: '✅' },
    { key: STATUS_PEDIDO.ENTREGUE, label: 'Entregues', icon: '🎉' }
  ];

  const handleStatusChange = async (pedidoId, novoStatus, feedback = '') => {
    try {
      await atualizarStatusPedido(pedidoId, novoStatus, feedback);
      
      // Limpar feedback input
      setFeedbackInputs(prev => ({ ...prev, [pedidoId]: '' }));
      setShowFeedback(prev => ({ ...prev, [pedidoId]: false }));
      
      console.log(`Status do pedido ${pedidoId} atualizado para: ${novoStatus}`);
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      alert('Erro ao atualizar status do pedido');
    }
  };

  const handleFeedbackChange = (pedidoId, value) => {
    setFeedbackInputs(prev => ({ ...prev, [pedidoId]: value }));
  };

  const toggleFeedback = (pedidoId) => {
    setShowFeedback(prev => ({ ...prev, [pedidoId]: !prev[pedidoId] }));
  };

  const getActionButtons = (pedido) => {
    const buttons = [];
    
    switch (pedido.status) {
      case STATUS_PEDIDO.AGUARDANDO:
        buttons.push(
          <ActionButton
            key="start"
            variant="start"
            onClick={() => handleStatusChange(pedido.id, STATUS_PEDIDO.EM_ANDAMENTO)}
          >
            👨‍🍳 Iniciar Preparo
          </ActionButton>
        );
        break;
        
      case STATUS_PEDIDO.EM_ANDAMENTO:
        buttons.push(
          <ActionButton
            key="ready"
            variant="ready"
            onClick={() => handleStatusChange(pedido.id, STATUS_PEDIDO.PRONTO)}
          >
            ✅ Marcar como Pronto
          </ActionButton>
        );
        break;
        
      case STATUS_PEDIDO.PRONTO:
        buttons.push(
          <ActionButton
            key="deliver"
            variant="deliver"
            onClick={() => handleStatusChange(pedido.id, STATUS_PEDIDO.ENTREGUE)}
          >
            🎉 Marcar como Entregue
          </ActionButton>
        );
        break;
    }
    
    // Botão de feedback sempre disponível (exceto para entregues)
    if (pedido.status !== STATUS_PEDIDO.ENTREGUE) {
      buttons.push(
        <ActionButton
          key="feedback"
          onClick={() => toggleFeedback(pedido.id)}
        >
          💬 {showFeedback[pedido.id] ? 'Cancelar' : 'Adicionar Feedback'}
        </ActionButton>
      );
    }
    
    // Botão de cancelar (exceto para entregues)
    if (pedido.status !== STATUS_PEDIDO.ENTREGUE) {
      buttons.push(
        <ActionButton
          key="cancel"
          variant="cancel"
          onClick={() => {
            if (confirm('Tem certeza que deseja cancelar este pedido?')) {
              handleStatusChange(pedido.id, STATUS_PEDIDO.CANCELADO, 'Pedido cancelado pelo funcionário');
            }
          }}
        >
          ❌ Cancelar
        </ActionButton>
      );
    }
    
    return buttons;
  };

  const pedidosFiltrados = obterPedidosPorStatus(activeTab);

  return (
    <Container>
      <Title>
        📱 Gerenciar Pedidos
      </Title>
      
      <TabContainer>
        {tabs.map(tab => (
          <Tab
            key={tab.key}
            active={activeTab === tab.key}
            count={obterPedidosPorStatus(tab.key).length}
            onClick={() => setActiveTab(tab.key)}
          >
            {tab.icon} {tab.label}
          </Tab>
        ))}
      </TabContainer>
      
      <PedidosList>
        {pedidosFiltrados.length === 0 ? (
          <EmptyState>
            {loading ? 'Carregando pedidos...' : `Nenhum pedido ${tabs.find(t => t.key === activeTab)?.label.toLowerCase()}`}
          </EmptyState>
        ) : (
          pedidosFiltrados.map(pedido => (
            <PedidoCard key={pedido.id} status={pedido.status}>
              <PedidoHeader>
                <PedidoId>{pedido.id}</PedidoId>
                <PedidoTime>
                  {new Date(pedido.dataCriacao).toLocaleString('pt-BR')}
                </PedidoTime>
              </PedidoHeader>
              
              <PedidoInfo>
                <InfoRow>
                  <InfoLabel>Cliente:</InfoLabel>
                  <InfoValue>{pedido.nomeCliente}</InfoValue>
                </InfoRow>
                <InfoRow>
                  <InfoLabel>Telefone:</InfoLabel>
                  <InfoValue>{pedido.telefone}</InfoValue>
                </InfoRow>
                {pedido.email && (
                  <InfoRow>
                    <InfoLabel>E-mail:</InfoLabel>
                    <InfoValue>{pedido.email}</InfoValue>
                  </InfoRow>
                )}
              </PedidoInfo>
              
              <PedidoItens>
                <ItensTitle>Itens do Pedido:</ItensTitle>
                <ItensText>{pedido.itens}</ItensText>
                {pedido.observacoes && (
                  <>
                    <ItensTitle style={{ marginTop: '10px' }}>Observações:</ItensTitle>
                    <ItensText>{pedido.observacoes}</ItensText>
                  </>
                )}
              </PedidoItens>
              
              {pedido.feedback && (
                <div style={{ 
                  background: '#fff3cd', 
                  padding: '10px', 
                  borderRadius: '8px', 
                  marginBottom: '15px',
                  borderLeft: '4px solid #ffc107'
                }}>
                  <strong>Feedback:</strong> {pedido.feedback}
                </div>
              )}
              
              <ActionsContainer>
                {getActionButtons(pedido)}
              </ActionsContainer>
              
              {showFeedback[pedido.id] && (
                <FeedbackContainer>
                  <FeedbackInput
                    placeholder="Digite um feedback para o cliente (ex: 'Não temos um dos itens, mas podemos substituir por...')"
                    value={feedbackInputs[pedido.id] || ''}
                    onChange={(e) => handleFeedbackChange(pedido.id, e.target.value)}
                  />
                  <FeedbackActions>
                    <ActionButton
                      variant="start"
                      onClick={() => {
                        const feedback = feedbackInputs[pedido.id] || '';
                        if (feedback.trim()) {
                          handleStatusChange(pedido.id, pedido.status, feedback);
                        } else {
                          alert('Digite um feedback antes de enviar');
                        }
                      }}
                    >
                      📤 Enviar Feedback
                    </ActionButton>
                    <ActionButton
                      onClick={() => toggleFeedback(pedido.id)}
                    >
                      ❌ Cancelar
                    </ActionButton>
                  </FeedbackActions>
                </FeedbackContainer>
              )}
            </PedidoCard>
          ))
        )}
      </PedidosList>
    </Container>
  );
};

export default GerenciarPedidos;
