import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

// Email do administrador
const ADMIN_EMAIL = '<EMAIL>';

/**
 * Middleware para verificar se o usuário é administrador
 * Verifica o token JWT e confirma se o email do usuário corresponde ao email do administrador
 */
const verificarAdmin = async (req, res, next) => {
  try {
    // Verificar se o token foi fornecido
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'Token não fornecido' });
    }

    // Verificar se o token é válido
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.userId = decoded.userId;
    
    // Buscar o usuário no banco de dados para verificar o email
    const db = req.app.locals.db;
    if (!db) {
      return res.status(500).json({ message: 'Erro de conexão com o banco de dados' });
    }
    
    const user = await db.collection('users').findOne({ _id: req.userId });
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Verificar se o email do usuário corresponde ao email do administrador
    if (user.email !== ADMIN_EMAIL) {
      return res.status(403).json({ 
        message: 'Acesso negado. Apenas o administrador pode acessar esta área.'
      });
    }
    
    // Se chegou até aqui, o usuário é o administrador
    req.isAdmin = true;
    next();
  } catch (error) {
    console.error('Erro ao verificar permissão de administrador:', error);
    res.status(401).json({ message: 'Token inválido ou expirado' });
  }
};

export default verificarAdmin;