import express from 'express';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
import jwt from 'jsonwebtoken';

// Carregar variáveis de ambiente
dotenv.config();

const router = express.Router();

// Middleware para verificar token JWT (importado do server.js)
const verificarToken = (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'Token não fornecido' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    console.error('Erro ao verificar token:', error);
    res.status(401).json({ message: 'Token inválido' });
  }
};

// Rota para obter configurações do usuário
router.get('/config', verificarToken, async (req, res) => {
  try {
    const userId = req.userId;
    console.log('Buscando configurações para usuário:', userId);
    
    // Usar a variável db global do server.js
    const users = db.collection('users');
    
    const user = await users.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Retornar apenas as configurações
    res.json(user.config || {});
  } catch (error) {
    console.error('Erro ao buscar configurações do usuário:', error);
    res.status(500).json({ 
      message: 'Erro ao buscar configurações',
      error: error.message 
    });
  }
});

// Rota para atualizar configurações do usuário
router.put('/config', verificarToken, async (req, res) => {
  try {
    const userId = req.userId;
    const { config } = req.body;
    
    if (!config) {
      return res.status(400).json({ message: 'Configurações não fornecidas' });
    }
    
    console.log('Atualizando configurações para usuário:', userId);
    
    // Usar a variável db global do server.js
    const users = db.collection('users');
    
    // Verificar se o usuário existe
    const user = await users.findOne({ _id: userId });
    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Atualizar apenas as configurações
    const result = await users.updateOne(
      { _id: userId },
      { $set: { config: config } }
    );
    
    if (result.matchedCount === 0) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }
    
    // Buscar usuário atualizado
    const updatedUser = await users.findOne({ _id: userId });
    const { password, ...userWithoutPassword } = updatedUser;
    
    console.log('Configurações atualizadas com sucesso para usuário:', userId);
    res.json(userWithoutPassword);
  } catch (error) {
    console.error('Erro ao atualizar configurações do usuário:', error);
    res.status(500).json({ 
      message: 'Erro ao atualizar configurações',
      error: error.message 
    });
  }
});

export default router;