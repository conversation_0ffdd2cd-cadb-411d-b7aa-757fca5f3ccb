import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

const PedidoContext = createContext();

export const usePedido = () => {
  const context = useContext(PedidoContext);
  if (!context) {
    throw new Error('usePedido deve ser usado dentro de um PedidoProvider');
  }
  return context;
};

export const PedidoProvider = ({ children }) => {
  const { user } = useAuth();
  const [pedidos, setPedidos] = useState([]);
  const [loading, setLoading] = useState(false);

  // Estados dos pedidos
  const STATUS_PEDIDO = {
    AGUARDANDO: 'aguardando',
    EM_ANDAMENTO: 'em_andamento', 
    PRONTO: 'pronto',
    ENTREGUE: 'entregue',
    CANCELADO: 'cancelado'
  };

  // Gerar ID único para pedido
  const gerarIdPedido = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `PED${timestamp}${random}`;
  };

  // Criar novo pedido
  const criarPedido = async (dadosPedido) => {
    try {
      setLoading(true);
      
      const novoPedido = {
        id: gerarIdPedido(),
        ...dadosPedido,
        status: STATUS_PEDIDO.AGUARDANDO,
        dataCriacao: new Date().toISOString(),
        dataAtualizacao: new Date().toISOString(),
        empresaId: user?.companyId || user?._id,
        feedback: '',
        observacoes: dadosPedido.observacoes || ''
      };

      // Salvar no localStorage (simulando backend)
      const pedidosExistentes = JSON.parse(localStorage.getItem('pedidos') || '[]');
      const novosPedidos = [...pedidosExistentes, novoPedido];
      localStorage.setItem('pedidos', JSON.stringify(novosPedidos));
      
      setPedidos(novosPedidos);
      
      console.log('Pedido criado:', novoPedido);
      return novoPedido;
    } catch (error) {
      console.error('Erro ao criar pedido:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Atualizar status do pedido
  const atualizarStatusPedido = async (pedidoId, novoStatus, feedback = '') => {
    try {
      setLoading(true);
      
      const pedidosExistentes = JSON.parse(localStorage.getItem('pedidos') || '[]');
      const pedidosAtualizados = pedidosExistentes.map(pedido => {
        if (pedido.id === pedidoId) {
          return {
            ...pedido,
            status: novoStatus,
            feedback: feedback || pedido.feedback,
            dataAtualizacao: new Date().toISOString()
          };
        }
        return pedido;
      });
      
      localStorage.setItem('pedidos', JSON.stringify(pedidosAtualizados));
      setPedidos(pedidosAtualizados);
      
      console.log(`Status do pedido ${pedidoId} atualizado para: ${novoStatus}`);
      return true;
    } catch (error) {
      console.error('Erro ao atualizar status do pedido:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Buscar pedido por ID
  const buscarPedidoPorId = (pedidoId) => {
    return pedidos.find(pedido => pedido.id === pedidoId);
  };

  // Carregar pedidos da empresa
  const carregarPedidos = () => {
    try {
      const pedidosExistentes = JSON.parse(localStorage.getItem('pedidos') || '[]');
      const empresaId = user?.companyId || user?._id;
      
      // Filtrar pedidos da empresa atual
      const pedidosDaEmpresa = pedidosExistentes.filter(pedido => 
        pedido.empresaId === empresaId
      );
      
      setPedidos(pedidosDaEmpresa);
    } catch (error) {
      console.error('Erro ao carregar pedidos:', error);
    }
  };

  // Obter pedidos por status
  const obterPedidosPorStatus = (status) => {
    return pedidos.filter(pedido => pedido.status === status);
  };

  // Obter estatísticas dos pedidos
  const obterEstatisticasPedidos = () => {
    const total = pedidos.length;
    const aguardando = pedidos.filter(p => p.status === STATUS_PEDIDO.AGUARDANDO).length;
    const emAndamento = pedidos.filter(p => p.status === STATUS_PEDIDO.EM_ANDAMENTO).length;
    const prontos = pedidos.filter(p => p.status === STATUS_PEDIDO.PRONTO).length;
    const entregues = pedidos.filter(p => p.status === STATUS_PEDIDO.ENTREGUE).length;
    const cancelados = pedidos.filter(p => p.status === STATUS_PEDIDO.CANCELADO).length;

    return {
      total,
      aguardando,
      emAndamento,
      prontos,
      entregues,
      cancelados
    };
  };

  // Gerar URL do QR Code para pedidos
  const gerarUrlQRCodePedidos = () => {
    const baseUrl = window.location.origin;
    const empresaId = user?.companyId || user?._id;
    return `${baseUrl}/fazer-pedido/${empresaId}`;
  };

  // Gerar URL de acompanhamento do pedido
  const gerarUrlAcompanhamento = (pedidoId) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/acompanhar-pedido/${pedidoId}`;
  };

  // Carregar pedidos quando o usuário mudar
  useEffect(() => {
    if (user) {
      carregarPedidos();
    }
  }, [user]);

  // Atualizar pedidos em tempo real (simulando)
  useEffect(() => {
    const interval = setInterval(() => {
      if (user) {
        carregarPedidos();
      }
    }, 5000); // Atualiza a cada 5 segundos

    return () => clearInterval(interval);
  }, [user]);

  const value = {
    // Estados
    pedidos,
    loading,
    STATUS_PEDIDO,
    
    // Funções
    criarPedido,
    atualizarStatusPedido,
    buscarPedidoPorId,
    carregarPedidos,
    obterPedidosPorStatus,
    obterEstatisticasPedidos,
    gerarUrlQRCodePedidos,
    gerarUrlAcompanhamento
  };

  return (
    <PedidoContext.Provider value={value}>
      {children}
    </PedidoContext.Provider>
  );
};
