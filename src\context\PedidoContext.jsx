import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import {
  criarPedido as criarPedidoAPI,
  buscarPedidos as buscarPedidosAPI,
  buscarPedidoPorId as buscarPedidoPorIdAPI,
  atualizarStatusPedido as atualizarStatusPedidoAPI
} from '../config/auth';

const PedidoContext = createContext();

export const usePedido = () => {
  const context = useContext(PedidoContext);
  if (!context) {
    throw new Error('usePedido deve ser usado dentro de um PedidoProvider');
  }
  return context;
};

export const PedidoProvider = ({ children }) => {
  const { user } = useAuth();
  const [pedidos, setPedidos] = useState([]);
  const [loading, setLoading] = useState(false);

  // Estados dos pedidos
  const STATUS_PEDIDO = {
    AGUARDANDO: 'aguardando',
    EM_ANDAMENTO: 'em_andamento',
    PRONTO: 'pronto',
    ENTREGUE: 'entregue',
    CANCELADO: 'cancelado'
  };

  // Gerar ID único para pedido
  const gerarIdPedido = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `PED${timestamp}${random}`;
  };

  // Criar novo pedido
  const criarPedido = async (dadosPedido) => {
    try {
      setLoading(true);

      const novoPedido = {
        ...dadosPedido,
        status: STATUS_PEDIDO.AGUARDANDO,
        dataCriacao: new Date().toISOString(),
        dataAtualizacao: new Date().toISOString(),
        empresaId: dadosPedido.empresaId || user?.companyId || user?._id,
        feedback: '',
        observacoes: dadosPedido.observacoes || ''
      };

      console.log('[PEDIDOS] Criando pedido:', novoPedido);
      console.log('[PEDIDOS] EmpresaId usado:', novoPedido.empresaId);

      // Criar pedido via API
      const pedidoCriado = await criarPedidoAPI(novoPedido);

      console.log('[PEDIDOS] Pedido criado via API:', pedidoCriado);

      // Atualizar estado local
      setPedidos(prev => {
        const novosPedidos = [...prev, pedidoCriado];
        console.log('[PEDIDOS] Estado local atualizado, total de pedidos:', novosPedidos.length);
        return novosPedidos;
      });

      return pedidoCriado;
    } catch (error) {
      console.error('[PEDIDOS] Erro ao criar pedido:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Atualizar status do pedido
  const atualizarStatusPedido = async (pedidoId, novoStatus, feedback = '') => {
    try {
      setLoading(true);

      // Atualizar via API
      const pedidoAtualizado = await atualizarStatusPedidoAPI(pedidoId, novoStatus, feedback);

      // Atualizar estado local
      setPedidos(prev => prev.map(pedido => {
        if (pedido._id === pedidoId || pedido.id === pedidoId) {
          return {
            ...pedido,
            ...pedidoAtualizado,
            status: novoStatus,
            feedback: feedback || pedido.feedback,
            dataAtualizacao: new Date().toISOString()
          };
        }
        return pedido;
      }));

      console.log(`Status do pedido ${pedidoId} atualizado para: ${novoStatus}`);
      return pedidoAtualizado;
    } catch (error) {
      console.error('Erro ao atualizar status do pedido:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Buscar pedido por ID
  const buscarPedidoPorId = async (pedidoId) => {
    try {
      // Primeiro tentar encontrar no estado local
      const pedidoLocal = pedidos.find(pedido => pedido._id === pedidoId || pedido.id === pedidoId);
      if (pedidoLocal) {
        return pedidoLocal;
      }

      // Se não encontrar localmente, buscar na API
      const pedidoAPI = await buscarPedidoPorIdAPI(pedidoId);
      return pedidoAPI;
    } catch (error) {
      console.error('Erro ao buscar pedido por ID:', error);
      return null;
    }
  };

  // Carregar pedidos da empresa
  const carregarPedidos = async () => {
    try {
      if (!user) {
        console.log('[PEDIDOS] Usuário não logado, não carregando pedidos');
        return;
      }

      setLoading(true);
      const empresaId = user?.companyId || user?._id;

      console.log('[PEDIDOS] Carregando pedidos para empresa:', empresaId);
      console.log('[PEDIDOS] Dados do usuário:', {
        id: user._id,
        companyId: user.companyId,
        email: user.email
      });

      // Buscar pedidos via API
      const pedidosDaEmpresa = await buscarPedidosAPI(empresaId);

      console.log('[PEDIDOS] Pedidos recebidos da API:', pedidosDaEmpresa);
      console.log('[PEDIDOS] Total de pedidos:', pedidosDaEmpresa.length);

      setPedidos(pedidosDaEmpresa);
    } catch (error) {
      console.error('[PEDIDOS] Erro ao carregar pedidos:', error);
      setPedidos([]); // Fallback para array vazio em caso de erro
    } finally {
      setLoading(false);
    }
  };

  // Obter pedidos por status
  const obterPedidosPorStatus = (status) => {
    return pedidos.filter(pedido => pedido.status === status);
  };

  // Obter estatísticas dos pedidos
  const obterEstatisticasPedidos = () => {
    const total = pedidos.length;
    const aguardando = pedidos.filter(p => p.status === STATUS_PEDIDO.AGUARDANDO).length;
    const emAndamento = pedidos.filter(p => p.status === STATUS_PEDIDO.EM_ANDAMENTO).length;
    const prontos = pedidos.filter(p => p.status === STATUS_PEDIDO.PRONTO).length;
    const entregues = pedidos.filter(p => p.status === STATUS_PEDIDO.ENTREGUE).length;
    const cancelados = pedidos.filter(p => p.status === STATUS_PEDIDO.CANCELADO).length;

    return {
      total,
      aguardando,
      emAndamento,
      prontos,
      entregues,
      cancelados
    };
  };

  // Gerar URL do QR Code para pedidos
  const gerarUrlQRCodePedidos = () => {
    const baseUrl = window.location.origin;
    const empresaId = user?.companyId || user?._id;
    return `${baseUrl}/fazer-pedido/${empresaId}`;
  };

  // Gerar URL de acompanhamento do pedido
  const gerarUrlAcompanhamento = (pedidoId) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/acompanhar-pedido/${pedidoId}`;
  };

  // Carregar pedidos quando o usuário mudar
  useEffect(() => {
    if (user) {
      carregarPedidos();
    }
  }, [user]);

  // Atualizar pedidos em tempo real (simulando)
  useEffect(() => {
    const interval = setInterval(() => {
      if (user) {
        carregarPedidos();
      }
    }, 5000); // Atualiza a cada 5 segundos

    return () => clearInterval(interval);
  }, [user]);

  const value = {
    // Estados
    pedidos,
    loading,
    STATUS_PEDIDO,

    // Funções
    criarPedido,
    atualizarStatusPedido,
    buscarPedidoPorId,
    carregarPedidos,
    obterPedidosPorStatus,
    obterEstatisticasPedidos,
    gerarUrlQRCodePedidos,
    gerarUrlAcompanhamento
  };

  return (
    <PedidoContext.Provider value={value}>
      {children}
    </PedidoContext.Provider>
  );
};
