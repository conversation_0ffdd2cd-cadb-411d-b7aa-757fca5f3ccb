import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';

const Container = styled.div`
  display: flex;
  height: 100vh;
  background: #f8f9fa;
`;

const ConfigPanel = styled.div`
  width: 400px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  padding: 20px;
`;

const PreviewPanel = styled.div`
  flex: 1;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
`;

const PreviewContainer = styled.div`
  width: 90%;
  height: 90%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  transform: scale(0.8);
  transform-origin: center;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin-bottom: 30px;
  font-size: 24px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
`;

const Section = styled.div`
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
`;

const SectionTitle = styled.h3`
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const FormGroup = styled.div`
  margin-bottom: 15px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-weight: 500;
  font-size: 14px;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  }
`;

const ColorInput = styled.input`
  width: 60px;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;

  &::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  &::-webkit-color-swatch {
    border: none;
    border-radius: 3px;
  }
`;

const ColorGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const ColorPreview = styled.div`
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background: ${props => props.color};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 20px;
`;

const Button = styled.button`
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;

  ${props => props.primary ? `
    background: #3498db;
    color: white;

    &:hover {
      background: #2980b9;
    }
  ` : `
    background: #ecf0f1;
    color: #2c3e50;

    &:hover {
      background: #d5dbdb;
    }
  `}
`;

const FileInput = styled.input`
  display: none;
`;

const FileButton = styled.label`
  display: inline-block;
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;

  &:hover {
    background: #2980b9;
  }
`;

// Componente de Preview do Painel
const PainelPreview = ({ config }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Função para calcular o layout baseado nas configurações
  const getLayoutStyle = () => {
    const layout = config.layoutConfig;
    const spacing = layout.spacing === 'small' ? '10px' :
                   layout.spacing === 'large' ? '25px' : '15px';

    let gridTemplate = '';
    switch (layout.gridLayout) {
      case '1-column':
        gridTemplate = '1fr';
        break;
      case '3-columns':
        gridTemplate = '1fr 1fr 1fr';
        break;
      case '2-columns':
      default:
        gridTemplate = '1fr 1fr';
        break;
    }

    return {
      display: 'grid',
      gridTemplateColumns: gridTemplate,
      gap: spacing,
      flex: 1,
      padding: spacing
    };
  };

  // Função para calcular posição da senha atual
  const getSenhaAtualStyle = () => {
    const layout = config.layoutConfig;
    let gridColumn = '1 / -1'; // Por padrão ocupa toda a largura
    let justifySelf = 'center';
    let alignSelf = 'center';

    switch (layout.senhaAtualPosition) {
      case 'top-left':
        gridColumn = '1';
        justifySelf = 'start';
        alignSelf = 'start';
        break;
      case 'top-right':
        gridColumn = '-1';
        justifySelf = 'end';
        alignSelf = 'start';
        break;
      case 'center':
        gridColumn = '1 / -1';
        justifySelf = 'center';
        alignSelf = 'center';
        break;
      case 'bottom-center':
        gridColumn = '1 / -1';
        justifySelf = 'center';
        alignSelf = 'end';
        break;
      default: // top-center
        gridColumn = '1 / -1';
        justifySelf = 'center';
        alignSelf = 'start';
        break;
    }

    const size = layout.senhaAtualSize;
    let padding = size === 'small' ? '10px' :
                 size === 'large' ? '30px' :
                 size === 'full' ? '40px' : '20px';

    return {
      background: config.senhaAtualBgColor,
      borderRadius: config.borderRadius,
      border: config.borderStyle !== 'none' ?
              `${config.borderWidth} ${config.borderStyle} ${config.borderColor}` : 'none',
      boxShadow: config.boxShadow,
      padding,
      textAlign: 'center',
      gridColumn,
      justifySelf,
      alignSelf,
      width: size === 'full' ? '100%' : 'auto',
      height: size === 'full' ? '100%' : 'auto'
    };
  };

  return (
    <div style={{
      width: '100%',
      height: '100%',
      background: config.backgroundType === 'color' ? config.backgroundColor :
                  config.backgroundImage ? `url(${config.backgroundImage})` : config.backgroundColor,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      fontFamily: config.fontFamily,
      color: config.textColor,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      {config.layoutConfig.headerHeight !== 'hidden' && (
        <div style={{
          background: config.headerColor,
          color: config.headerTextColor,
          padding: config.layoutConfig.headerHeight === 'small' ? '8px 15px' :
                   config.layoutConfig.headerHeight === 'large' ? '25px 20px' : '15px 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: config.logoPosition === 'center' ? 'center' :
                         config.logoPosition === 'right' ? 'flex-end' : 'flex-start'
        }}>
          {config.logo && (
            <img
              src={config.logo}
              alt="Logo"
              style={{
                maxHeight: config.logoSize === 'small' ? '20px' :
                          config.logoSize === 'large' ? '40px' : '30px',
                marginRight: config.logoPosition !== 'right' ? '10px' : '0',
                marginLeft: config.logoPosition === 'right' ? '10px' : '0'
              }}
            />
          )}
          <h1 style={{
            margin: 0,
            fontSize: config.layoutConfig.headerHeight === 'small' ? '16px' :
                     config.layoutConfig.headerHeight === 'large' ? '24px' : '20px',
            fontFamily: config.fontFamily
          }}>
            {config.title}
          </h1>
        </div>
      )}

      {/* Conteúdo Principal */}
      <div style={getLayoutStyle()}>
        {/* Senha Atual */}
        <div style={getSenhaAtualStyle()}>
          <h3 style={{
            margin: '0 0 10px 0',
            color: config.textColor,
            fontFamily: config.fontFamily,
            fontSize: config.layoutConfig.senhaAtualSize === 'small' ? '12px' : '14px'
          }}>
            Senha Atual
          </h3>
          <div style={{
            fontSize: config.layoutConfig.senhaAtualSize === 'small' ? '24px' :
                     config.layoutConfig.senhaAtualSize === 'large' ? '48px' :
                     config.layoutConfig.senhaAtualSize === 'full' ? '60px' : '36px',
            fontWeight: 'bold',
            color: config.senhaColor,
            textShadow: config.textShadow,
            fontFamily: config.fontFamily,
            margin: '5px 0'
          }}>
            N001
          </div>
          <div style={{
            color: config.textColor,
            fontSize: config.layoutConfig.senhaAtualSize === 'small' ? '10px' : '12px'
          }}>
            Guichê 1
          </div>
        </div>

        {/* Últimas Senhas */}
        {config.layoutConfig.ultimasSenhasPosition !== 'hidden' && (
          <div style={{
            background: config.senhasListBgColor,
            borderRadius: config.borderRadius,
            border: config.borderStyle !== 'none' ?
                    `${config.borderWidth} ${config.borderStyle} ${config.borderColor}` : 'none',
            boxShadow: config.boxShadow,
            padding: '10px',
            gridColumn: config.layoutConfig.ultimasSenhasPosition === 'bottom' ? '1 / -1' : 'auto'
          }}>
            <h4 style={{
              margin: '0 0 8px 0',
              color: config.textColor,
              fontFamily: config.fontFamily,
              fontSize: '12px'
            }}>
              Últimas Senhas
            </h4>
            {['P002', 'N003', 'R001'].map((senha, index) => (
              <div key={index} style={{
                background: config.itemBgColor,
                borderRadius: config.itemBorderRadius,
                boxShadow: config.itemBoxShadow,
                padding: '6px',
                margin: '3px 0',
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '10px'
              }}>
                <span style={{
                  color: config.tipoColors[senha[0]] || config.tipoColors.default,
                  fontWeight: 'bold',
                  fontFamily: config.fontFamily
                }}>
                  {senha}
                </span>
                <span>G{index + 1}</span>
              </div>
            ))}
          </div>
        )}

        {/* Senhas Aguardando */}
        {config.layoutConfig.aguardandoPosition !== 'hidden' && (
          <div style={{
            background: config.senhasListBgColor,
            borderRadius: config.borderRadius,
            border: config.borderStyle !== 'none' ?
                    `${config.borderWidth} ${config.borderStyle} ${config.borderColor}` : 'none',
            boxShadow: config.boxShadow,
            padding: '10px',
            gridColumn: config.layoutConfig.aguardandoPosition === 'bottom' ? '1 / -1' : 'auto'
          }}>
            <h4 style={{
              margin: '0 0 8px 0',
              color: config.textColor,
              fontFamily: config.fontFamily,
              fontSize: '12px'
            }}>
              Aguardando
            </h4>
            {['P003', 'N004', 'N005'].map((senha, index) => (
              <div key={index} style={{
                background: config.itemBgColor,
                borderRadius: config.itemBorderRadius,
                boxShadow: config.itemBoxShadow,
                padding: '6px',
                margin: '3px 0',
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '10px'
              }}>
                <span style={{
                  color: config.tipoColors[senha[0]] || config.tipoColors.default,
                  fontWeight: 'bold',
                  fontFamily: config.fontFamily
                }}>
                  {senha}
                </span>
                <span>{index + 2}min</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {!config.hideFooter && config.layoutConfig.footerHeight !== 'hidden' && (
        <div style={{
          background: config.footerColor,
          color: config.footerTextColor,
          padding: config.layoutConfig.footerHeight === 'small' ? '5px 15px' :
                   config.layoutConfig.footerHeight === 'large' ? '15px 20px' : '8px 15px',
          textAlign: 'center',
          fontSize: config.layoutConfig.footerHeight === 'small' ? '10px' : '12px'
        }}>
          {!config.hideReloj && (
            <div style={{ marginBottom: '3px' }}>
              {formatTime(currentTime)} - {formatDate(currentTime)}
            </div>
          )}
          {config.footerText && (
            <div>{config.footerText}</div>
          )}
        </div>
      )}
    </div>
  );
};

const ConfiguracaoDesign = () => {
  const { user } = useAuth();
  const [config, setConfig] = useState({
    // Layout Geral
    title: 'Painel de Senhas',
    backgroundColor: '#f5f5f5',
    backgroundType: 'color',
    backgroundImage: null,
    fontFamily: 'Arial',
    textColor: '#333333',

    // Header
    headerColor: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
    headerTextColor: '#ffffff',

    // Logo
    logo: null,
    logoPosition: 'left',
    logoSize: 'medium',

    // Senha Atual
    senhaAtualBgColor: '#ffffff',
    senhaColor: '#3498db',
    fontSize: 120,
    textShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',

    // Listas de Senhas
    senhasListBgColor: '#ffffff',
    itemBgColor: '#f8f9fa',
    itemBorderRadius: '8px',
    itemBoxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',

    // Bordas e Sombras
    borderRadius: '12px',
    borderStyle: 'none',
    borderColor: '#dddddd',
    borderWidth: '1px',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',

    // Footer
    footerColor: 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)',
    footerTextColor: '#ffffff',
    footerText: 'Sistema de Gerenciamento de Senhas',
    hideFooter: false,
    hideReloj: false,

    // Cores por Tipo
    tipoColors: {
      P: '#e74c3c',
      N: '#3498db',
      R: '#2ecc71',
      default: '#7f8c8d'
    },

    // Configurações Avançadas
    customCss: '',
    layoutTheme: 'default',

    // Posicionamento dos Elementos
    layoutConfig: {
      senhaAtualPosition: 'top-center', // top-left, top-center, top-right, center, bottom-center
      senhaAtualSize: 'large', // small, medium, large, full
      ultimasSenhasPosition: 'left', // left, right, bottom, hidden
      aguardandoPosition: 'right', // left, right, bottom, hidden
      headerHeight: 'medium', // small, medium, large, hidden
      footerHeight: 'small', // small, medium, large, hidden
      gridLayout: '2-columns', // 1-column, 2-columns, 3-columns, custom
      spacing: 'medium', // small, medium, large
      elementOrder: ['header', 'senhaAtual', 'listas', 'footer']
    }
  });

  // Carregar configuração salva
  useEffect(() => {
    const savedConfig = localStorage.getItem('painelConfig');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Erro ao carregar configuração:', error);
      }
    }
  }, []);

  // Salvar configuração
  const salvarConfig = () => {
    localStorage.setItem('painelConfig', JSON.stringify(config));
    alert('Configuração salva com sucesso!');
  };

  // Resetar para padrão
  const resetarConfig = () => {
    if (confirm('Tem certeza que deseja resetar todas as configurações?')) {
      localStorage.removeItem('painelConfig');
      window.location.reload();
    }
  };

  // Atualizar configuração
  const updateConfig = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Upload de logo
  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        updateConfig('logo', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Upload de imagem de fundo
  const handleBackgroundUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        updateConfig('backgroundImage', e.target.result);
        updateConfig('backgroundType', 'image');
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Container>
      <ConfigPanel>
        <Title>🎨 Configuração de Design</Title>

        {/* Seção Layout Geral */}
        <Section>
          <SectionTitle>🏠 Layout Geral</SectionTitle>

          <FormGroup>
            <Label>Título do Painel</Label>
            <Input
              type="text"
              value={config.title}
              onChange={(e) => updateConfig('title', e.target.value)}
              placeholder="Ex: Painel de Senhas"
            />
          </FormGroup>

          <FormGroup>
            <Label>Tipo de Fundo</Label>
            <Select
              value={config.backgroundType}
              onChange={(e) => updateConfig('backgroundType', e.target.value)}
            >
              <option value="color">Cor Sólida</option>
              <option value="image">Imagem</option>
            </Select>
          </FormGroup>

          {config.backgroundType === 'color' ? (
            <FormGroup>
              <Label>Cor de Fundo</Label>
              <ColorGroup>
                <ColorInput
                  type="color"
                  value={config.backgroundColor}
                  onChange={(e) => updateConfig('backgroundColor', e.target.value)}
                />
                <Input
                  type="text"
                  value={config.backgroundColor}
                  onChange={(e) => updateConfig('backgroundColor', e.target.value)}
                  placeholder="#f5f5f5"
                />
              </ColorGroup>
            </FormGroup>
          ) : (
            <FormGroup>
              <Label>Imagem de Fundo</Label>
              <FileButton htmlFor="background-upload">
                📁 Escolher Imagem
              </FileButton>
              <FileInput
                id="background-upload"
                type="file"
                accept="image/*"
                onChange={handleBackgroundUpload}
              />
            </FormGroup>
          )}

          <FormGroup>
            <Label>Fonte</Label>
            <Select
              value={config.fontFamily}
              onChange={(e) => updateConfig('fontFamily', e.target.value)}
            >
              <option value="Arial">Arial</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Georgia">Georgia</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Verdana">Verdana</option>
              <option value="Roboto">Roboto</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Cor do Texto</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.textColor}
                onChange={(e) => updateConfig('textColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.textColor}
                onChange={(e) => updateConfig('textColor', e.target.value)}
                placeholder="#333333"
              />
            </ColorGroup>
          </FormGroup>
        </Section>

        {/* Seção Header */}
        <Section>
          <SectionTitle>📋 Cabeçalho</SectionTitle>

          <FormGroup>
            <Label>Cor do Cabeçalho</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.headerColor.includes('gradient') ? '#2c3e50' : config.headerColor}
                onChange={(e) => updateConfig('headerColor', e.target.value)}
              />
              <Select
                value={config.headerColor.includes('gradient') ? 'gradient' : 'solid'}
                onChange={(e) => {
                  if (e.target.value === 'gradient') {
                    updateConfig('headerColor', 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)');
                  } else {
                    updateConfig('headerColor', '#2c3e50');
                  }
                }}
              >
                <option value="solid">Cor Sólida</option>
                <option value="gradient">Gradiente</option>
              </Select>
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Cor do Texto do Cabeçalho</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.headerTextColor}
                onChange={(e) => updateConfig('headerTextColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.headerTextColor}
                onChange={(e) => updateConfig('headerTextColor', e.target.value)}
                placeholder="#ffffff"
              />
            </ColorGroup>
          </FormGroup>
        </Section>

        {/* Seção Logo */}
        <Section>
          <SectionTitle>🖼️ Logo</SectionTitle>

          <FormGroup>
            <Label>Upload do Logo</Label>
            <FileButton htmlFor="logo-upload">
              📁 Escolher Logo
            </FileButton>
            <FileInput
              id="logo-upload"
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
            />
            {config.logo && (
              <div style={{ marginTop: '10px' }}>
                <img src={config.logo} alt="Logo Preview" style={{ maxHeight: '50px' }} />
              </div>
            )}
          </FormGroup>

          <FormGroup>
            <Label>Posição do Logo</Label>
            <Select
              value={config.logoPosition}
              onChange={(e) => updateConfig('logoPosition', e.target.value)}
            >
              <option value="left">Esquerda</option>
              <option value="center">Centro</option>
              <option value="right">Direita</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Tamanho do Logo</Label>
            <Select
              value={config.logoSize}
              onChange={(e) => updateConfig('logoSize', e.target.value)}
            >
              <option value="small">Pequeno</option>
              <option value="medium">Médio</option>
              <option value="large">Grande</option>
            </Select>
          </FormGroup>
        </Section>

        {/* Seção Senha Atual */}
        <Section>
          <SectionTitle>🎯 Senha Atual</SectionTitle>

          <FormGroup>
            <Label>Cor de Fundo da Senha Atual</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.senhaAtualBgColor}
                onChange={(e) => updateConfig('senhaAtualBgColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.senhaAtualBgColor}
                onChange={(e) => updateConfig('senhaAtualBgColor', e.target.value)}
                placeholder="#ffffff"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Cor da Senha</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.senhaColor}
                onChange={(e) => updateConfig('senhaColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.senhaColor}
                onChange={(e) => updateConfig('senhaColor', e.target.value)}
                placeholder="#3498db"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Tamanho da Fonte (px)</Label>
            <Input
              type="number"
              value={config.fontSize}
              onChange={(e) => updateConfig('fontSize', parseInt(e.target.value))}
              min="60"
              max="200"
            />
          </FormGroup>
        </Section>

        {/* Seção Cores por Tipo */}
        <Section>
          <SectionTitle>🎨 Cores por Tipo de Senha</SectionTitle>

          <FormGroup>
            <Label>Prioritária (P)</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.tipoColors.P}
                onChange={(e) => updateConfig('tipoColors', { ...config.tipoColors, P: e.target.value })}
              />
              <Input
                type="text"
                value={config.tipoColors.P}
                onChange={(e) => updateConfig('tipoColors', { ...config.tipoColors, P: e.target.value })}
                placeholder="#e74c3c"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Normal (N)</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.tipoColors.N}
                onChange={(e) => updateConfig('tipoColors', { ...config.tipoColors, N: e.target.value })}
              />
              <Input
                type="text"
                value={config.tipoColors.N}
                onChange={(e) => updateConfig('tipoColors', { ...config.tipoColors, N: e.target.value })}
                placeholder="#3498db"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Rápida (R)</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.tipoColors.R}
                onChange={(e) => updateConfig('tipoColors', { ...config.tipoColors, R: e.target.value })}
              />
              <Input
                type="text"
                value={config.tipoColors.R}
                onChange={(e) => updateConfig('tipoColors', { ...config.tipoColors, R: e.target.value })}
                placeholder="#2ecc71"
              />
            </ColorGroup>
          </FormGroup>
        </Section>

        {/* Seção Listas de Senhas */}
        <Section>
          <SectionTitle>📋 Listas de Senhas</SectionTitle>

          <FormGroup>
            <Label>Cor de Fundo das Listas</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.senhasListBgColor}
                onChange={(e) => updateConfig('senhasListBgColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.senhasListBgColor}
                onChange={(e) => updateConfig('senhasListBgColor', e.target.value)}
                placeholder="#ffffff"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Cor de Fundo dos Itens</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.itemBgColor}
                onChange={(e) => updateConfig('itemBgColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.itemBgColor}
                onChange={(e) => updateConfig('itemBgColor', e.target.value)}
                placeholder="#f8f9fa"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Raio da Borda dos Itens</Label>
            <Input
              type="text"
              value={config.itemBorderRadius}
              onChange={(e) => updateConfig('itemBorderRadius', e.target.value)}
              placeholder="8px"
            />
          </FormGroup>
        </Section>

        {/* Seção Bordas e Sombras */}
        <Section>
          <SectionTitle>🔲 Bordas e Sombras</SectionTitle>

          <FormGroup>
            <Label>Raio da Borda</Label>
            <Input
              type="text"
              value={config.borderRadius}
              onChange={(e) => updateConfig('borderRadius', e.target.value)}
              placeholder="12px"
            />
          </FormGroup>

          <FormGroup>
            <Label>Estilo da Borda</Label>
            <Select
              value={config.borderStyle}
              onChange={(e) => updateConfig('borderStyle', e.target.value)}
            >
              <option value="none">Nenhuma</option>
              <option value="solid">Sólida</option>
              <option value="dashed">Tracejada</option>
              <option value="dotted">Pontilhada</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Cor da Borda</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.borderColor}
                onChange={(e) => updateConfig('borderColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.borderColor}
                onChange={(e) => updateConfig('borderColor', e.target.value)}
                placeholder="#dddddd"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Largura da Borda</Label>
            <Input
              type="text"
              value={config.borderWidth}
              onChange={(e) => updateConfig('borderWidth', e.target.value)}
              placeholder="1px"
            />
          </FormGroup>

          <FormGroup>
            <Label>Sombra</Label>
            <Input
              type="text"
              value={config.boxShadow}
              onChange={(e) => updateConfig('boxShadow', e.target.value)}
              placeholder="0 8px 24px rgba(0, 0, 0, 0.15)"
            />
          </FormGroup>
        </Section>

        {/* Seção Footer */}
        <Section>
          <SectionTitle>📄 Rodapé</SectionTitle>

          <FormGroup>
            <Label>Cor do Rodapé</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.footerColor.includes('gradient') ? '#2c3e50' : config.footerColor}
                onChange={(e) => updateConfig('footerColor', e.target.value)}
              />
              <Select
                value={config.footerColor.includes('gradient') ? 'gradient' : 'solid'}
                onChange={(e) => {
                  if (e.target.value === 'gradient') {
                    updateConfig('footerColor', 'linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%)');
                  } else {
                    updateConfig('footerColor', '#2c3e50');
                  }
                }}
              >
                <option value="solid">Cor Sólida</option>
                <option value="gradient">Gradiente</option>
              </Select>
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Cor do Texto do Rodapé</Label>
            <ColorGroup>
              <ColorInput
                type="color"
                value={config.footerTextColor}
                onChange={(e) => updateConfig('footerTextColor', e.target.value)}
              />
              <Input
                type="text"
                value={config.footerTextColor}
                onChange={(e) => updateConfig('footerTextColor', e.target.value)}
                placeholder="#ffffff"
              />
            </ColorGroup>
          </FormGroup>

          <FormGroup>
            <Label>Texto do Rodapé</Label>
            <Input
              type="text"
              value={config.footerText}
              onChange={(e) => updateConfig('footerText', e.target.value)}
              placeholder="Sistema de Gerenciamento de Senhas"
            />
          </FormGroup>

          <FormGroup>
            <Label>
              <input
                type="checkbox"
                checked={config.hideFooter}
                onChange={(e) => updateConfig('hideFooter', e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              Ocultar Rodapé
            </Label>
          </FormGroup>

          <FormGroup>
            <Label>
              <input
                type="checkbox"
                checked={config.hideReloj}
                onChange={(e) => updateConfig('hideReloj', e.target.checked)}
                style={{ marginRight: '8px' }}
              />
              Ocultar Relógio
            </Label>
          </FormGroup>
        </Section>

        {/* Seção Posicionamento dos Elementos */}
        <Section>
          <SectionTitle>📐 Posicionamento dos Elementos</SectionTitle>

          <FormGroup>
            <Label>Layout do Grid</Label>
            <Select
              value={config.layoutConfig.gridLayout}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, gridLayout: e.target.value })}
            >
              <option value="1-column">1 Coluna</option>
              <option value="2-columns">2 Colunas</option>
              <option value="3-columns">3 Colunas</option>
              <option value="custom">Personalizado</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Posição da Senha Atual</Label>
            <Select
              value={config.layoutConfig.senhaAtualPosition}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, senhaAtualPosition: e.target.value })}
            >
              <option value="top-left">Superior Esquerda</option>
              <option value="top-center">Superior Centro</option>
              <option value="top-right">Superior Direita</option>
              <option value="center">Centro</option>
              <option value="bottom-center">Inferior Centro</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Tamanho da Senha Atual</Label>
            <Select
              value={config.layoutConfig.senhaAtualSize}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, senhaAtualSize: e.target.value })}
            >
              <option value="small">Pequeno</option>
              <option value="medium">Médio</option>
              <option value="large">Grande</option>
              <option value="full">Tela Cheia</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Posição das Últimas Senhas</Label>
            <Select
              value={config.layoutConfig.ultimasSenhasPosition}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, ultimasSenhasPosition: e.target.value })}
            >
              <option value="left">Esquerda</option>
              <option value="right">Direita</option>
              <option value="bottom">Inferior</option>
              <option value="hidden">Ocultar</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Posição das Senhas Aguardando</Label>
            <Select
              value={config.layoutConfig.aguardandoPosition}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, aguardandoPosition: e.target.value })}
            >
              <option value="left">Esquerda</option>
              <option value="right">Direita</option>
              <option value="bottom">Inferior</option>
              <option value="hidden">Ocultar</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Altura do Cabeçalho</Label>
            <Select
              value={config.layoutConfig.headerHeight}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, headerHeight: e.target.value })}
            >
              <option value="small">Pequeno</option>
              <option value="medium">Médio</option>
              <option value="large">Grande</option>
              <option value="hidden">Ocultar</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Altura do Rodapé</Label>
            <Select
              value={config.layoutConfig.footerHeight}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, footerHeight: e.target.value })}
            >
              <option value="small">Pequeno</option>
              <option value="medium">Médio</option>
              <option value="large">Grande</option>
              <option value="hidden">Ocultar</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Espaçamento entre Elementos</Label>
            <Select
              value={config.layoutConfig.spacing}
              onChange={(e) => updateConfig('layoutConfig', { ...config.layoutConfig, spacing: e.target.value })}
            >
              <option value="small">Pequeno</option>
              <option value="medium">Médio</option>
              <option value="large">Grande</option>
            </Select>
          </FormGroup>
        </Section>

        <ButtonGroup>
          <Button primary onClick={salvarConfig}>
            💾 Salvar Configuração
          </Button>
          <Button onClick={resetarConfig}>
            🔄 Resetar
          </Button>
        </ButtonGroup>
      </ConfigPanel>

      <PreviewPanel>
        <PreviewContainer>
          <PainelPreview config={config} />
        </PreviewContainer>
      </PreviewPanel>
    </Container>
  );
};

export default ConfiguracaoDesign;
