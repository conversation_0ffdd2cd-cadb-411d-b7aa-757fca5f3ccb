const ColorPicker = ({ label, value, onChange }) => (
  <div className="mb-3">
    <label className="form-label">{label}</label>
    <input
      type="color"
      className="form-control form-control-color w-100"
      value={value}
      onChange={onChange}
    />
  </div>
);

const LayoutConfig = ({ theme, onThemeChange }) => {
  const handleColorChange = (key, value) => {
    const newTheme = { ...theme };
    newTheme.colors[key].value = value;
    onThemeChange(newTheme);
  };

  return (
    <div className="p-3">
      <h5 className="mb-3">Personalização de Cores</h5>
      
      <div className="mb-4">
        <h6>Cabeçalho</h6>
        <ColorPicker
          label="Fundo do Cabeçalho"
          value={theme.colors.header.value}
          onChange={(e) => handleColorChange('header', e.target.value)}
        />
        <ColorPicker
          label="Texto do Cabeçalho"
          value={theme.colors.headerText.value}
          onChange={(e) => handleColorChange('headerText', e.target.value)}
        />
        <ColorPicker
          label="Destaque do Cabeçalho"
          value={theme.colors.headerAccent.value}
          onChange={(e) => handleColorChange('headerAccent', e.target.value)}
        />
      </div>

      <div className="mb-4">
        <h6>Rodapé</h6>
        <ColorPicker
          label="Fundo do Rodapé"
          value={theme.colors.footer.value}
          onChange={(e) => handleColorChange('footer', e.target.value)}
        />
        <ColorPicker
          label="Texto do Rodapé"
          value={theme.colors.footerText.value}
          onChange={(e) => handleColorChange('footerText', e.target.value)}
        />
        <ColorPicker
          label="Destaque do Rodapé"
          value={theme.colors.footerAccent.value}
          onChange={(e) => handleColorChange('footerAccent', e.target.value)}
        />
      </div>

      <div className="mb-4">
        <h6>Itens da Lista</h6>
        <ColorPicker
          label="Fundo dos Itens"
          value={theme.colors.itemBg.value}
          onChange={(e) => handleColorChange('itemBg', e.target.value)}
        />
        <ColorPicker
          label="Fundo dos Itens (Hover)"
          value={theme.colors.itemHoverBg.value}
          onChange={(e) => handleColorChange('itemHoverBg', e.target.value)}
        />
        <ColorPicker
          label="Texto do Guichê"
          value={theme.colors.guicheText.value}
          onChange={(e) => handleColorChange('guicheText', e.target.value)}
        />
      </div>

      <div className="mb-4">
        <h6>Cores por Tipo</h6>
        <ColorPicker
          label="Tipo P - Preferencial"
          value={theme.colors.tipo_P.value}
          onChange={(e) => handleColorChange('tipo_P', e.target.value)}
        />
        <ColorPicker
          label="Tipo N - Normal"
          value={theme.colors.tipo_N.value}
          onChange={(e) => handleColorChange('tipo_N', e.target.value)}
        />
        <ColorPicker
          label="Tipo R - Retorno"
          value={theme.colors.tipo_R.value}
          onChange={(e) => handleColorChange('tipo_R', e.target.value)}
        />
      </div>
    </div>
  );
};

export default LayoutConfig; 